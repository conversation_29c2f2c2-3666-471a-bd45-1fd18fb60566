export interface LuminarApp {
    id: string;
    name: string;
    shortName: string;
    description: string;
    url: string;
    icon: string;
    color: string;
    roles: string[];
    permissions: string[];
    category: 'core' | 'training' | 'management' | 'analytics';
    status: 'active' | 'beta' | 'coming-soon';
}
export declare const LUMINAR_APPS: LuminarApp[];
export declare function getAppById(id: string): LuminarApp | undefined;
export declare function getAppsByCategory(category: LuminarApp['category']): LuminarApp[];
export declare function getActiveApps(): LuminarApp[];
export declare function getAppsForUser(roles: string[], permissions: string[]): LuminarApp[];
export declare const APP_ENVIRONMENTS: {
    readonly development: {
        readonly apiUrl: "http://localhost:3000/api/v1";
        readonly wsUrl: "ws://localhost:3000";
        readonly domain: "localhost";
    };
    readonly staging: {
        readonly apiUrl: "https://api-staging.luminar.com";
        readonly wsUrl: "wss://api-staging.luminar.com";
        readonly domain: ".luminar-staging.com";
    };
    readonly production: {
        readonly apiUrl: "https://api.luminar.com";
        readonly wsUrl: "wss://api.luminar.com";
        readonly domain: ".luminar.com";
    };
};
export type Environment = keyof typeof APP_ENVIRONMENTS;
export declare function getEnvironmentConfig(env?: Environment): {
    readonly apiUrl: "http://localhost:3000/api/v1";
    readonly wsUrl: "ws://localhost:3000";
    readonly domain: "localhost";
} | {
    readonly apiUrl: "https://api-staging.luminar.com";
    readonly wsUrl: "wss://api-staging.luminar.com";
    readonly domain: ".luminar-staging.com";
} | {
    readonly apiUrl: "https://api.luminar.com";
    readonly wsUrl: "wss://api.luminar.com";
    readonly domain: ".luminar.com";
};
export declare const SHARED_CONFIG: {
    companyName: string;
    supportEmail: string;
    helpUrl: string;
    privacyUrl: string;
    termsUrl: string;
    logo: string;
    favicon: string;
    theme: {
        primaryColor: string;
        secondaryColor: string;
        accentColor: string;
        backgroundColor: string;
        textColor: string;
    };
    auth: {
        sessionTimeout: number;
        refreshInterval: number;
        passwordPolicy: {
            minLength: number;
            requireUppercase: boolean;
            requireLowercase: boolean;
            requireNumbers: boolean;
            requireSpecialChars: boolean;
        };
    };
    features: {
        enableSSO: boolean;
        enableMFA: boolean;
        enableOfflineMode: boolean;
        enablePushNotifications: boolean;
        enableAnalytics: boolean;
    };
};
//# sourceMappingURL=luminar-apps.config.d.ts.map