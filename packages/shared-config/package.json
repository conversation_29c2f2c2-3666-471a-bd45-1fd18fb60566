{"name": "@luminar/shared-config", "version": "1.0.0", "description": "Shared configuration utilities for Luminar applications", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "rm -rf dist && tsc --emitDeclarationOnly && tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@luminar/shared-core": "workspace:*"}, "devDependencies": {"@types/node": "^22.5.4", "eslint": "^9.15.0", "tsup": "^8.0.0", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "publishConfig": {"access": "restricted"}}