{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": false, "removeComments": false, "composite": false, "outDir": "./dist", "rootDir": "./src"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.*", "**/*.spec.*"]}