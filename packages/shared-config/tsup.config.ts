import { defineConfig } from 'tsup'

export default defineConfig({
  entry: {
    index: 'src/index.ts',
  },
  format: ['cjs', 'esm'],
  dts: false, // Disable DTS generation through tsup
  clean: false, // Don't clean to preserve TypeScript-generated files
  sourcemap: true,
  external: [],
  treeshake: true,
  splitting: false,
  minify: false, // Keep readable for debugging
  target: 'es2020',
  tsconfig: './tsconfig.json',
})