CREATE TABLE "typeorm"."user_sessions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "sessionId" character varying NOT NULL,
        "userId" uuid NOT NULL,
        "ipAddress" inet,
        "userAgent" text,
        "status" "typeorm"."user_sessions_status_enum" NOT NULL DEFAULT 'active',
        "lastActivity" TIMESTAMP NOT NULL DEFAULT now(),
        "expiresAt" TIMESTAMP NOT NULL,
        "deviceFingerprint" character varying,
        "issuedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "metadata" jsonb DEFAULT '{}',
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_9996bd5fed30b5d2592b9bd46c7" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_5f2c9cc5d1ddd8b0fb2c0df5a4e" UNIQUE ("sessionId")
      ) CREATE EXTENSION IF NOT EXISTS "uuid-ossp" CREATE TABLE "typeorm"."typeorm_migrations" ("id" SERIAL NOT NULL, "timestamp" bigint NOT NULL, "name" character varying NOT NULL, CONSTRAINT "PK_bb2f075707dd300ba86d0208923" PRIMARY KEY ("id")) SELECT * FROM current_database() SELECT * FROM "typeorm"."typeorm_migrations" "typeorm_migrations" ORDER BY "id" DESC CREATE INDEX "idx_user_username" ON "typeorm"."users" ("username") SELECT id, email, username, roles, permissions, "isActive", "isEmailVerified" FROM typeorm.users WHERE email = $1 INSERT INTO typeorm.users (email, password, "firstName", "lastName", username, roles, permissions, "isActive", "isEmailVerified") VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) CREATE INDEX "IDX_5f2c9cc5d1ddd8b0fb2c0df5a4" ON "typeorm"."user_sessions" ("sessionId") CREATE TABLE "typeorm"."users" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "email" character varying NOT NULL,
        "password" character varying NOT NULL,
        "firstName" character varying,
        "lastName" character varying,
        "username" character varying,
        "avatarUrl" character varying,
        "phoneNumber" character varying(20),
        "bio" text,
        "preferences" jsonb NOT NULL DEFAULT '{}',
        "metadata" jsonb NOT NULL DEFAULT '{}',
        "roles" character varying array NOT NULL DEFAULT array[]::character varying[],
        "permissions" character varying array NOT NULL DEFAULT array[]::character varying[],
        "isActive" boolean NOT NULL DEFAULT true,
        "isEmailVerified" boolean NOT NULL DEFAULT false,
        "emailVerifiedAt" TIMESTAMP,
        "lastLoginAt" TIMESTAMP,
        "lastLoginIp" inet,
        "loginCount" integer NOT NULL DEFAULT 0,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "deletedAt" TIMESTAMP,
        CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email")
      ) SELECT version() CREATE TABLE "typeorm"."roles" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "displayName" character varying NOT NULL,
        "description" text,
        "type" "typeorm"."roles_type_enum" NOT NULL DEFAULT 'custom',
        "permissions" character varying array NOT NULL DEFAULT array[]::character varying[],
        "isActive" boolean NOT NULL DEFAULT true,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_648e3f5447f725579d7d4ffdfb7" UNIQUE ("name")
      ) SELECT "n"."nspname", "t"."typname" FROM "pg_type" "t" INNER JOIN "pg_namespace" "n" ON "n"."oid" = "t"."typnamespace" WHERE "n"."nspname" = $1 AND "t"."typname" = $2 CREATE INDEX "IDX_610102b60fea1455310ccd299d" ON "typeorm"."refresh_tokens" ("userId") SELECT c.relchecks, c.relkind, c.relhasindex, c.relhasrules, c.relhastriggers, c.relrowsecurity, c.relforcerowsecurity, $1 AS relhasoids, c.relispartition, $2, c.reltablespace, CASE WHEN c.reloftype = $3 THEN $4 ELSE c.reloftype::pg_catalog.regtype::pg_catalog.text END, c.relpersistence, c.relreplident, am.amname
FROM pg_catalog.pg_class c
 LEFT JOIN pg_catalog.pg_class tc ON (c.reltoastrelid = tc.oid)
LEFT JOIN pg_catalog.pg_am am ON (c.relam = am.oid)
WHERE c.oid = $5 SELECT a.attname,
  pg_catalog.format_type(a.atttypid, a.atttypmod),
  (SELECT pg_catalog.pg_get_expr(d.adbin, d.adrelid, $1)
   FROM pg_catalog.pg_attrdef d
   WHERE d.adrelid = a.attrelid AND d.adnum = a.attnum AND a.atthasdef),
  a.attnotnull,
  (SELECT c.collname FROM pg_catalog.pg_collation c, pg_catalog.pg_type t
   WHERE c.oid = a.attcollation AND t.oid = a.atttypid AND a.attcollation <> t.typcollation) AS attcollation,
  a.attidentity,
  a.attgenerated
FROM pg_catalog.pg_attribute a
WHERE a.attrelid = $2 AND a.attnum > $3 AND NOT a.attisdropped
ORDER BY a.attnum INSERT INTO "typeorm"."typeorm_migrations"("timestamp", "name") VALUES ($1, $2) SELECT pubname
     , $1
     , $2
FROM pg_catalog.pg_publication p
     JOIN pg_catalog.pg_publication_namespace pn ON p.oid = pn.pnpubid
     JOIN pg_catalog.pg_class pc ON pc.relnamespace = pn.pnnspid
WHERE pc.oid =$3 and pg_catalog.pg_relation_is_publishable($4)
UNION
SELECT pubname
     , pg_get_expr(pr.prqual, c.oid)
     , (CASE WHEN pr.prattrs IS NOT NULL THEN
         (SELECT string_agg(attname, $5)
           FROM pg_catalog.generate_series($6, pg_catalog.array_upper(pr.prattrs::pg_catalog.int2[], $7)) s,
                pg_catalog.pg_attribute
          WHERE attrelid = pr.prrelid AND attnum = prattrs[s])
        ELSE $8 END) FROM pg_catalog.pg_publication p
     JOIN pg_catalog.pg_publication_rel pr ON p.oid = pr.prpubid
     JOIN pg_catalog.pg_class c ON c.oid = pr.prrelid
WHERE pr.prrelid = $9
UNION
SELECT pubname
     , $10
     , $11
FROM pg_catalog.pg_publication p
WHERE p.puballtables AND pg_catalog.pg_relation_is_publishable($12)
ORDER BY 1 SELECT $1 as sametable, conname,
  pg_catalog.pg_get_constraintdef(r.oid, $2) as condef,
  conrelid::pg_catalog.regclass AS ontable
FROM pg_catalog.pg_constraint r
WHERE r.conrelid = $3 AND r.contype = $4
     AND conparentid = $5
ORDER BY conname COMMIT CREATE INDEX "IDX_13275383dcdf095ee29f2b3455" ON "typeorm"."user_sessions" ("userId") CREATE INDEX "idx_user_email" ON "typeorm"."users" ("email") CREATE TABLE "typeorm"."permissions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" character varying NOT NULL,
        "displayName" character varying NOT NULL,
        "description" text,
        "resource" character varying,
        "action" character varying,
        "scope" character varying array NOT NULL DEFAULT array[]::character varying[],
        "isActive" boolean NOT NULL DEFAULT true,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_920331560282b8bd21bb02290df" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_48ce552495d14eae9b187bb6716" UNIQUE ("name")
      ) START TRANSACTION SELECT d.datname as "Name",
       pg_catalog.pg_get_userbyid(d.datdba) as "Owner",
       pg_catalog.pg_encoding_to_char(d.encoding) as "Encoding",
       d.datcollate as "Collate",
       d.datctype as "Ctype",
       d.daticulocale as "ICU Locale",
       CASE d.datlocprovider WHEN $1 THEN $2 WHEN $3 THEN $4 END AS "Locale Provider",
       pg_catalog.array_to_string(d.datacl, $5) AS "Access privileges"
FROM pg_catalog.pg_database d
ORDER BY 1 CREATE TABLE "typeorm"."refresh_tokens" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "token" character varying NOT NULL,
        "userId" uuid NOT NULL,
        "isRevoked" boolean NOT NULL DEFAULT false,
        "expiresAt" TIMESTAMP NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "deviceInfo" jsonb,
        "ipAddress" inet,
        "userAgent" text,
        CONSTRAINT "PK_7d8bee0204106019488c4c50ffa" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_c31d0a2f38e6e99110df62ab0af" UNIQUE ("token")
      ) CREATE TYPE "typeorm"."roles_type_enum" AS ENUM('system', 'organization', 'custom') SELECT c.oid::pg_catalog.regclass, c.relkind, inhdetachpending, pg_catalog.pg_get_expr(c.relpartbound, c.oid)
FROM pg_catalog.pg_class c, pg_catalog.pg_inherits i
WHERE c.oid = i.inhrelid AND i.inhparent = $1
ORDER BY pg_catalog.pg_get_expr(c.relpartbound, c.oid) = $2, c.oid::pg_catalog.regclass::pg_catalog.text SELECT c.oid,
  n.nspname,
  c.relname
FROM pg_catalog.pg_class c
     LEFT JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
WHERE c.relname OPERATOR(pg_catalog.~) $1 COLLATE pg_catalog.default
  AND n.nspname OPERATOR(pg_catalog.~) $2 COLLATE pg_catalog.default
ORDER BY 2, 3 SELECT * FROM "information_schema"."tables" WHERE "table_schema" = $1 AND "table_name" = $2 CREATE INDEX "idx_user_created" ON "typeorm"."users" ("createdAt") CREATE TYPE "typeorm"."user_sessions_status_enum" AS ENUM('active', 'expired', 'revoked') SELECT "table_schema", "table_name", obj_description(($1 || "table_schema" || $2 || "table_name" || $3)::regclass, $4) AS table_comment FROM "information_schema"."tables" WHERE ("table_schema" = $5 AND "table_name" = $6) OR ("table_schema" = $7 AND "table_name" = $8) OR ("table_schema" = $9 AND "table_name" = $10) OR ("table_schema" = $11 AND "table_name" = $12) OR ("table_schema" = $13 AND "table_name" = $14) OR ("table_schema" = $15 AND "table_name" = $16) OR ("table_schema" = $17 AND "table_name" = $18) OR ("table_schema" = $19 AND "table_name" = $20) OR ("table_schema" = $21 AND "table_name" = $22) OR ("table_schema" = $23 AND "table_name" = $24) OR ("table_schema" = $25 AND "table_name" = $26) OR ("table_schema" = $27 AND "table_name" = $28) OR ("table_schema" = $29 AND "table_name" = $30) OR ("table_schema" = $31 AND "table_name" = $32) OR ("table_schema" = $33 AND "table_name" = $34) OR ("table_schema" = $35 AND "table_name" = $36) OR ("table_schema" = $37 AND "table_name" = $38) OR ("table_schema" = $39 AND "table_name" = $40) OR ("table_schema" = $41 AND "table_name" = $42) OR ("table_schema" = $43 AND "table_name" = $44) OR ("table_schema" = $45 AND "table_name" = $46) OR ("table_schema" = $47 AND "table_name" = $48) OR ("table_schema" = $49 AND "table_name" = $50) OR ("table_schema" = $51 AND "table_name" = $52) OR ("table_schema" = $53 AND "table_name" = $54) OR ("table_schema" = $55 AND "table_name" = $56) OR ("table_schema" = $57 AND "table_name" = $58) OR ("table_schema" = $59 AND "table_name" = $60) OR ("table_schema" = $61 AND "table_name" = $62) OR ("table_schema" = $63 AND "table_name" = $64) OR ("table_schema" = $65 AND "table_name" = $66) OR ("table_schema" = $67 AND "table_name" = $68) OR ("table_schema" = $69 AND "table_name" = $70) OR ("table_schema" = $71 AND "table_name" = $72) OR ("table_schema" = $73 AND "table_name" = $74) OR ("table_schema" = $75 AND "table_name" = $76) OR ("table_schema" = $77 AND "table_name" = $78) OR ("table_schema" = $79 AND "table_name" = $80) OR ("table_schema" = $81 AND "table_name" = $82) OR ("table_schema" = $83 AND "table_name" = $84) OR ("table_schema" = $85 AND "table_name" = $86) OR ("table_schema" = $87 AND "table_name" = $88) OR ("table_schema" = $89 AND "table_name" = $90) OR ("table_schema" = $91 AND "table_name" = $92) OR ("table_schema" = $93 AND "table_name" = $94) OR ("table_schema" = $95 AND "table_name" = $96) SELECT t.tgname, pg_catalog.pg_get_triggerdef(t.oid, $1), t.tgenabled, t.tgisinternal,
  CASE WHEN t.tgparentid != $2 THEN
    (SELECT u.tgrelid::pg_catalog.regclass
     FROM pg_catalog.pg_trigger AS u,
          pg_catalog.pg_partition_ancestors(t.tgrelid) WITH ORDINALITY AS a(relid, depth)
     WHERE u.tgname = t.tgname AND u.tgrelid = a.relid
           AND u.tgparentid = $3
     ORDER BY a.depth LIMIT $4)
  END AS parent
FROM pg_catalog.pg_trigger t
WHERE t.tgrelid = $5 AND (NOT t.tgisinternal OR (t.tgisinternal AND t.tgenabled = $6))
ORDER BY 1 CREATE INDEX "idx_user_active_status" ON "typeorm"."users" ("isActive", "deletedAt") SELECT c2.relname, i.indisprimary, i.indisunique, i.indisclustered, i.indisvalid, pg_catalog.pg_get_indexdef(i.indexrelid, $1, $2),
  pg_catalog.pg_get_constraintdef(con.oid, $3), contype, condeferrable, condeferred, i.indisreplident, c2.reltablespace
FROM pg_catalog.pg_class c, pg_catalog.pg_class c2, pg_catalog.pg_index i
  LEFT JOIN pg_catalog.pg_constraint con ON (conrelid = i.indrelid AND conindid = i.indexrelid AND contype IN ($4,$5,$6))
WHERE c.oid = $7 AND c.oid = i.indrelid AND i.indexrelid = c2.oid
ORDER BY i.indisprimary DESC, c2.relname SELECT tablename FROM pg_tables WHERE schemaname = $1 ORDER BY tablename ALTER TABLE "typeorm"."refresh_tokens" 
      ADD CONSTRAINT "FK_610102b60fea1455310ccd299de" 
      FOREIGN KEY ("userId") REFERENCES "typeorm"."users"("id") ON DELETE CASCADE ON UPDATE NO ACTION SELECT oid, stxrelid::pg_catalog.regclass, stxnamespace::pg_catalog.regnamespace::pg_catalog.text AS nsp, stxname,
pg_catalog.pg_get_statisticsobjdef_columns(oid) AS columns,
  $1 = any(stxkind) AS ndist_enabled,
  $2 = any(stxkind) AS deps_enabled,
  $3 = any(stxkind) AS mcv_enabled,
stxstattarget
FROM pg_catalog.pg_statistic_ext
WHERE stxrelid = $4
ORDER BY nsp, stxname CREATE SCHEMA IF NOT EXISTS typeorm CREATE INDEX "idx_user_email_unique" ON "typeorm"."users" ("email") SELECT tablename FROM pg_tables WHERE schemaname = $1 SELECT n.nspname as "Schema",
  c.relname as "Name",
  CASE c.relkind WHEN $1 THEN $2 WHEN $3 THEN $4 WHEN $5 THEN $6 WHEN $7 THEN $8 WHEN $9 THEN $10 WHEN $11 THEN $12 WHEN $13 THEN $14 WHEN $15 THEN $16 WHEN $17 THEN $18 END as "Type",
  pg_catalog.pg_get_userbyid(c.relowner) as "Owner"
FROM pg_catalog.pg_class c
     LEFT JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
     LEFT JOIN pg_catalog.pg_am am ON am.oid = c.relam
WHERE c.relkind IN ($19,$20,$21)
      AND n.nspname <> $22
      AND n.nspname !~ $23
      AND n.nspname <> $24
  AND pg_catalog.pg_table_is_visible(c.oid)
ORDER BY 1,2 SELECT * FROM current_schema() SELECT conname, conrelid::pg_catalog.regclass AS ontable,
       pg_catalog.pg_get_constraintdef(oid, $1) AS condef
  FROM pg_catalog.pg_constraint c
 WHERE confrelid IN (SELECT pg_catalog.pg_partition_ancestors($2)
                     UNION ALL VALUES ($3::pg_catalog.regclass))
       AND contype = $4 AND conparentid = $5
ORDER BY conname ALTER TABLE "typeorm"."user_sessions" 
      ADD CONSTRAINT "FK_13275383dcdf095ee29f2b3455a" 
      FOREIGN KEY ("userId") REFERENCES "typeorm"."users"("id") ON DELETE CASCADE ON UPDATE NO ACTION SELECT c.oid::pg_catalog.regclass
FROM pg_catalog.pg_class c, pg_catalog.pg_inherits i
WHERE c.oid = i.inhparent AND i.inhrelid = $1
  AND c.relkind != $2 AND c.relkind != $3
ORDER BY inhseqno SELECT pol.polname, pol.polpermissive,
  CASE WHEN pol.polroles = $1 THEN $2 ELSE pg_catalog.array_to_string(array(select rolname from pg_catalog.pg_roles where oid = any (pol.polroles) order by 1),$3) END,
  pg_catalog.pg_get_expr(pol.polqual, pol.polrelid),
  pg_catalog.pg_get_expr(pol.polwithcheck, pol.polrelid),
  CASE pol.polcmd
    WHEN $4 THEN $5
    WHEN $6 THEN $7
    WHEN $8 THEN $9
    WHEN $10 THEN $11
    END AS cmd
FROM pg_catalog.pg_policy pol
WHERE pol.polrelid = $12 ORDER BY 1 