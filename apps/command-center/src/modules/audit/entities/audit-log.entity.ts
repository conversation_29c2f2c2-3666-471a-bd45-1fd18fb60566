import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

export enum AuditEventType {
  // Authentication events
  LOGIN = 'login',
  LOGOUT = 'logout',
  LOGIN_FAILED = 'login_failed',
  PASSWORD_CHANGE = 'password_change',
  ACCOUNT_LOCKED = 'account_locked',
  ACCOUNT_UNLOCKED = 'account_unlocked',

  // Data access events
  DATA_ACCESS = 'data_access',
  DATA_EXPORT = 'data_export',
  DATA_IMPORT = 'data_import',
  DATA_SEARCH = 'data_search',
  DATA_VIEW = 'data_view',

  // Data modification events
  DATA_CREATE = 'data_create',
  DATA_UPDATE = 'data_update',
  DATA_DELETE = 'data_delete',
  DATA_RESTORE = 'data_restore',
  DATA_ARCHIVE = 'data_archive',

  // System events
  SYSTEM_START = 'system_start',
  SYSTEM_STOP = 'system_stop',
  SYSTEM_ERROR = 'system_error',
  SYSTEM_WARNING = 'system_warning',
  SYSTEM_CONFIG_CHANGE = 'system_config_change',

  // Security events
  SECURITY_VIOLATION = 'security_violation',
  PERMISSION_DENIED = 'permission_denied',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  SECURITY_SCAN = 'security_scan',

  // Compliance events
  CONSENT_GIVEN = 'consent_given',
  CONSENT_WITHDRAWN = 'consent_withdrawn',
  DATA_RETENTION_EXECUTED = 'data_retention_executed',
  DATA_ANONYMIZED = 'data_anonymized',
  COMPLIANCE_REPORT_GENERATED = 'compliance_report_generated',

  // Administrative events
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  ROLE_ASSIGNED = 'role_assigned',
  ROLE_REMOVED = 'role_removed',
  PERMISSION_GRANTED = 'permission_granted',
  PERMISSION_REVOKED = 'permission_revoked',

  // HTTP events
  HTTP_REQUEST = 'http_request',
}

export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum AuditStatus {
  SUCCESS = 'success',
  FAILURE = 'failure',
  WARNING = 'warning',
  INFO = 'info',
}

@Entity('audit_logs')
@Index(['eventType', 'timestamp'])
@Index(['userId', 'timestamp'])
@Index(['severity', 'timestamp'])
@Index(['status', 'timestamp'])
@Index(['ipAddress', 'timestamp'])
@Index(['sessionId', 'timestamp'])
export class AuditLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: AuditEventType,
  })
  eventType: AuditEventType;

  @Column({
    type: 'enum',
    enum: AuditSeverity,
    default: AuditSeverity.LOW,
  })
  severity: AuditSeverity;

  @Column({
    type: 'enum',
    enum: AuditStatus,
    default: AuditStatus.SUCCESS,
  })
  status: AuditStatus;

  @Column({ type: 'uuid', nullable: true })
  userId?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  username?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  sessionId?: string;

  @Column({ type: 'inet', nullable: true })
  ipAddress?: string;

  @Column({ type: 'text', nullable: true })
  userAgent?: string;

  @Column({ type: 'varchar', length: 255 })
  resource: string; // What was accessed/modified

  @Column({ type: 'varchar', length: 255 })
  action: string; // What action was performed

  @Column({ type: 'text' })
  description: string; // Human-readable description

  @Column({ type: 'jsonb', nullable: true })
  details?: Record<string, any>; // Additional event details

  @Column({ type: 'jsonb', nullable: true })
  oldValues?: Record<string, any>; // Previous values (for updates)

  @Column({ type: 'jsonb', nullable: true })
  newValues?: Record<string, any>; // New values (for updates)

  @Column({ type: 'varchar', length: 255, nullable: true })
  requestId?: string; // Request ID for tracing

  @Column({ type: 'varchar', length: 255, nullable: true })
  correlationId?: string; // Correlation ID for related events

  @Column({ type: 'varchar', length: 255 })
  source: string; // Source system/component

  @Column({ type: 'varchar', length: 255, nullable: true })
  sourceVersion?: string; // Version of source system

  @Column({ type: 'varchar', length: 255, nullable: true })
  environment?: string; // Environment (dev, staging, prod)

  @Column({ type: 'integer', nullable: true })
  duration?: number; // Duration in milliseconds

  @Column({ type: 'text', nullable: true })
  errorMessage?: string; // Error message if applicable

  @Column({ type: 'text', nullable: true })
  stackTrace?: string; // Stack trace if applicable

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>; // Additional metadata

  @Column({ type: 'boolean', default: false })
  isSecurityRelated: boolean;

  @Column({ type: 'boolean', default: false })
  isComplianceRelated: boolean;

  @Column({ type: 'boolean', default: false })
  requiresReview: boolean;

  @Column({ type: 'boolean', default: false })
  isReviewed: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  reviewedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  reviewedAt?: Date;

  @Column({ type: 'text', nullable: true })
  reviewNotes?: string;

  @Column({ type: 'integer', default: 0 })
  retentionDays: number; // How long to retain this log

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  timestamp: Date;

  @CreateDateColumn()
  createdAt: Date;

  // Helper methods
  isHighSeverity(): boolean {
    return this.severity === AuditSeverity.HIGH || this.severity === AuditSeverity.CRITICAL;
  }

  isFailure(): boolean {
    return this.status === AuditStatus.FAILURE;
  }

  isSecurityEvent(): boolean {
    return this.isSecurityRelated || [
      AuditEventType.SECURITY_VIOLATION,
      AuditEventType.PERMISSION_DENIED,
      AuditEventType.UNAUTHORIZED_ACCESS,
      AuditEventType.SUSPICIOUS_ACTIVITY,
      AuditEventType.LOGIN_FAILED,
      AuditEventType.ACCOUNT_LOCKED,
    ].includes(this.eventType);
  }

  isComplianceEvent(): boolean {
    return this.isComplianceRelated || [
      AuditEventType.CONSENT_GIVEN,
      AuditEventType.CONSENT_WITHDRAWN,
      AuditEventType.DATA_RETENTION_EXECUTED,
      AuditEventType.DATA_ANONYMIZED,
      AuditEventType.COMPLIANCE_REPORT_GENERATED,
    ].includes(this.eventType);
  }

  needsReview(): boolean {
    return this.requiresReview && !this.isReviewed;
  }

  isExpired(): boolean {
    if (this.retentionDays === 0) return false;
    
    const expiryDate = new Date(this.createdAt);
    expiryDate.setDate(expiryDate.getDate() + this.retentionDays);
    return expiryDate < new Date();
  }

  getAgeInDays(): number {
    const now = new Date();
    const ageInMs = now.getTime() - this.timestamp.getTime();
    return Math.floor(ageInMs / (1000 * 60 * 60 * 24));
  }

  generateFingerprint(): string {
    const crypto = require('crypto');
    const data = `${this.eventType}:${this.userId}:${this.resource}:${this.action}`;
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 16);
  }
}