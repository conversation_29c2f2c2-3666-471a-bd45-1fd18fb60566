import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { AuditLog, AuditEventType, AuditSeverity, AuditStatus } from '../entities/audit-log.entity';
import { SecurityEvent } from '../entities/security-event.entity';

export interface AuditLogEntry {
  eventType: AuditEventType;
  severity?: AuditSeverity;
  status?: AuditStatus;
  userId?: string;
  username?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  resource: string;
  action: string;
  description: string;
  details?: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  requestId?: string;
  correlationId?: string;
  source: string;
  sourceVersion?: string;
  environment?: string;
  duration?: number;
  errorMessage?: string;
  stackTrace?: string;
  metadata?: Record<string, any>;
  isSecurityRelated?: boolean;
  isComplianceRelated?: boolean;
  requiresReview?: boolean;
  retentionDays?: number;
}

export interface AuditLogQuery {
  eventTypes?: AuditEventType[];
  severities?: AuditSeverity[];
  statuses?: AuditStatus[];
  userIds?: string[];
  resources?: string[];
  actions?: string[];
  sources?: string[];
  startDate?: Date;
  endDate?: Date;
  isSecurityRelated?: boolean;
  isComplianceRelated?: boolean;
  requiresReview?: boolean;
  limit?: number;
  offset?: number;
}

export interface AuditLogStats {
  totalEvents: number;
  eventsByType: Record<AuditEventType, number>;
  eventsBySeverity: Record<AuditSeverity, number>;
  eventsByStatus: Record<AuditStatus, number>;
  securityEvents: number;
  complianceEvents: number;
  eventsRequiringReview: number;
  topUsers: Array<{ userId: string; username: string; count: number }>;
  topResources: Array<{ resource: string; count: number }>;
  topActions: Array<{ action: string; count: number }>;
  averageEventsPerDay: number;
  errorRate: number;
}

@Injectable()
export class AuditLogService {
  private readonly logger = new Logger(AuditLogService.name);

  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    @InjectRepository(SecurityEvent)
    private readonly securityEventRepository: Repository<SecurityEvent>,
    @InjectQueue('audit-processing')
    private readonly auditQueue: Queue,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async log(entry: AuditLogEntry): Promise<AuditLog> {
    try {
      const auditLog = this.auditLogRepository.create({
        ...entry,
        severity: entry.severity || AuditSeverity.LOW,
        status: entry.status || AuditStatus.SUCCESS,
        environment: entry.environment || process.env.NODE_ENV || 'development',
        retentionDays: entry.retentionDays || this.getDefaultRetentionDays(entry.eventType),
        timestamp: new Date(),
      });

      const savedLog = await this.auditLogRepository.save(auditLog);

      // Add to processing queue for analysis
      await this.auditQueue.add('analyze-audit-log', {
        auditLogId: savedLog.id,
        eventType: entry.eventType,
        severity: entry.severity || AuditSeverity.LOW,
      });

      // Emit event for real-time monitoring
      this.eventEmitter.emit('audit.log.created', {
        id: savedLog.id,
        eventType: entry.eventType,
        severity: entry.severity || AuditSeverity.LOW,
        isSecurityRelated: entry.isSecurityRelated || false,
        timestamp: savedLog.timestamp,
      });

      // Log high severity events immediately
      if (savedLog.isHighSeverity()) {
        this.logger.warn(`High severity audit event: ${entry.eventType} - ${entry.description}`);
      }

      return savedLog;
    } catch (error) {
      this.logger.error('Failed to create audit log:', error);
      throw error;
    }
  }

  async logSecurity(
    eventType: AuditEventType,
    description: string,
    details?: Record<string, any>,
    severity: AuditSeverity = AuditSeverity.HIGH
  ): Promise<AuditLog> {
    return this.log({
      eventType,
      severity,
      resource: 'security',
      action: 'security_event',
      description,
      details,
      source: 'security-system',
      isSecurityRelated: true,
      requiresReview: true,
    });
  }

  async logCompliance(
    eventType: AuditEventType,
    description: string,
    details?: Record<string, any>,
    severity: AuditSeverity = AuditSeverity.MEDIUM
  ): Promise<AuditLog> {
    return this.log({
      eventType,
      severity,
      resource: 'compliance',
      action: 'compliance_event',
      description,
      details,
      source: 'compliance-system',
      isComplianceRelated: true,
      requiresReview: true,
    });
  }

  async logUserActivity(
    userId: string,
    username: string,
    action: string,
    resource: string,
    description: string,
    details?: Record<string, any>,
    request?: any
  ): Promise<AuditLog> {
    return this.log({
      eventType: AuditEventType.DATA_ACCESS,
      userId,
      username,
      sessionId: request?.sessionId,
      ipAddress: request?.ip,
      userAgent: request?.headers?.['user-agent'],
      resource,
      action,
      description,
      details,
      requestId: request?.id,
      source: 'user-activity',
    });
  }

  async logSystemEvent(
    eventType: AuditEventType,
    description: string,
    details?: Record<string, any>,
    severity: AuditSeverity = AuditSeverity.LOW
  ): Promise<AuditLog> {
    return this.log({
      eventType,
      severity,
      resource: 'system',
      action: 'system_event',
      description,
      details,
      source: 'system',
    });
  }

  async logDataChange(
    userId: string,
    username: string,
    resource: string,
    action: string,
    oldValues: Record<string, any>,
    newValues: Record<string, any>,
    request?: any
  ): Promise<AuditLog> {
    return this.log({
      eventType: action === 'create' ? AuditEventType.DATA_CREATE : 
                 action === 'update' ? AuditEventType.DATA_UPDATE : 
                 AuditEventType.DATA_DELETE,
      userId,
      username,
      sessionId: request?.sessionId,
      ipAddress: request?.ip,
      userAgent: request?.headers?.['user-agent'],
      resource,
      action,
      description: `${action} ${resource}`,
      oldValues,
      newValues,
      requestId: request?.id,
      source: 'data-change',
    });
  }

  async query(queryParams: AuditLogQuery): Promise<AuditLog[]> {
    const query = this.auditLogRepository.createQueryBuilder('audit');

    if (queryParams.eventTypes?.length) {
      query.andWhere('audit.eventType IN (:...eventTypes)', { eventTypes: queryParams.eventTypes });
    }

    if (queryParams.severities?.length) {
      query.andWhere('audit.severity IN (:...severities)', { severities: queryParams.severities });
    }

    if (queryParams.statuses?.length) {
      query.andWhere('audit.status IN (:...statuses)', { statuses: queryParams.statuses });
    }

    if (queryParams.userIds?.length) {
      query.andWhere('audit.userId IN (:...userIds)', { userIds: queryParams.userIds });
    }

    if (queryParams.resources?.length) {
      query.andWhere('audit.resource IN (:...resources)', { resources: queryParams.resources });
    }

    if (queryParams.actions?.length) {
      query.andWhere('audit.action IN (:...actions)', { actions: queryParams.actions });
    }

    if (queryParams.sources?.length) {
      query.andWhere('audit.source IN (:...sources)', { sources: queryParams.sources });
    }

    if (queryParams.startDate && queryParams.endDate) {
      query.andWhere('audit.timestamp BETWEEN :startDate AND :endDate', {
        startDate: queryParams.startDate,
        endDate: queryParams.endDate,
      });
    }

    if (queryParams.isSecurityRelated !== undefined) {
      query.andWhere('audit.isSecurityRelated = :isSecurityRelated', {
        isSecurityRelated: queryParams.isSecurityRelated,
      });
    }

    if (queryParams.isComplianceRelated !== undefined) {
      query.andWhere('audit.isComplianceRelated = :isComplianceRelated', {
        isComplianceRelated: queryParams.isComplianceRelated,
      });
    }

    if (queryParams.requiresReview !== undefined) {
      query.andWhere('audit.requiresReview = :requiresReview', {
        requiresReview: queryParams.requiresReview,
      });
    }

    query.orderBy('audit.timestamp', 'DESC');

    if (queryParams.limit) {
      query.limit(queryParams.limit);
    }

    if (queryParams.offset) {
      query.offset(queryParams.offset);
    }

    return query.getMany();
  }

  async getStats(startDate: Date, endDate: Date): Promise<AuditLogStats> {
    const logs = await this.auditLogRepository.find({
      where: {
        timestamp: Between(startDate, endDate),
      },
    });

    const stats: AuditLogStats = {
      totalEvents: logs.length,
      eventsByType: {} as Record<AuditEventType, number>,
      eventsBySeverity: {} as Record<AuditSeverity, number>,
      eventsByStatus: {} as Record<AuditStatus, number>,
      securityEvents: 0,
      complianceEvents: 0,
      eventsRequiringReview: 0,
      topUsers: [],
      topResources: [],
      topActions: [],
      averageEventsPerDay: 0,
      errorRate: 0,
    };

    // Initialize counters
    Object.values(AuditEventType).forEach(type => {
      stats.eventsByType[type] = 0;
    });
    Object.values(AuditSeverity).forEach(severity => {
      stats.eventsBySeverity[severity] = 0;
    });
    Object.values(AuditStatus).forEach(status => {
      stats.eventsByStatus[status] = 0;
    });

    const userCounts: Record<string, { userId: string; username: string; count: number }> = {};
    const resourceCounts: Record<string, number> = {};
    const actionCounts: Record<string, number> = {};
    let errorCount = 0;

    logs.forEach(log => {
      // Count by type
      stats.eventsByType[log.eventType]++;

      // Count by severity
      stats.eventsBySeverity[log.severity]++;

      // Count by status
      stats.eventsByStatus[log.status]++;

      // Count security events
      if (log.isSecurityRelated) {
        stats.securityEvents++;
      }

      // Count compliance events
      if (log.isComplianceRelated) {
        stats.complianceEvents++;
      }

      // Count events requiring review
      if (log.requiresReview) {
        stats.eventsRequiringReview++;
      }

      // Count users
      if (log.userId) {
        if (!userCounts[log.userId]) {
          userCounts[log.userId] = {
            userId: log.userId,
            username: log.username || 'Unknown',
            count: 0,
          };
        }
        userCounts[log.userId].count++;
      }

      // Count resources
      resourceCounts[log.resource] = (resourceCounts[log.resource] || 0) + 1;

      // Count actions
      actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;

      // Count errors
      if (log.status === AuditStatus.FAILURE) {
        errorCount++;
      }
    });

    // Calculate top users
    stats.topUsers = Object.values(userCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate top resources
    stats.topResources = Object.entries(resourceCounts)
      .map(([resource, count]) => ({ resource, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate top actions
    stats.topActions = Object.entries(actionCounts)
      .map(([action, count]) => ({ action, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate average events per day
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    stats.averageEventsPerDay = daysDiff > 0 ? logs.length / daysDiff : 0;

    // Calculate error rate
    stats.errorRate = logs.length > 0 ? (errorCount / logs.length) * 100 : 0;

    return stats;
  }

  async getEventsRequiringReview(): Promise<AuditLog[]> {
    return this.auditLogRepository.find({
      where: {
        requiresReview: true,
        isReviewed: false,
      },
      order: {
        timestamp: 'DESC',
      },
    });
  }

  async markAsReviewed(id: string, reviewedBy: string, reviewNotes?: string): Promise<AuditLog> {
    const log = await this.auditLogRepository.findOne({ where: { id } });
    if (!log) {
      throw new Error(`Audit log ${id} not found`);
    }

    log.isReviewed = true;
    log.reviewedBy = reviewedBy;
    log.reviewedAt = new Date();
    log.reviewNotes = reviewNotes;

    return this.auditLogRepository.save(log);
  }

  async cleanupExpiredLogs(): Promise<number> {
    const expiredLogs = await this.auditLogRepository
      .createQueryBuilder('audit')
      .where('audit.retentionDays > 0')
      .andWhere('audit.createdAt < NOW() - INTERVAL audit.retentionDays DAY')
      .getMany();

    if (expiredLogs.length === 0) {
      return 0;
    }

    const expiredIds = expiredLogs.map(log => log.id);
    await this.auditLogRepository.delete(expiredIds);

    this.logger.log(`Cleaned up ${expiredLogs.length} expired audit logs`);
    return expiredLogs.length;
  }

  async exportLogs(startDate: Date, endDate: Date, format: 'json' | 'csv' = 'json'): Promise<string> {
    const logs = await this.auditLogRepository.find({
      where: {
        timestamp: Between(startDate, endDate),
      },
      order: {
        timestamp: 'DESC',
      },
    });

    if (format === 'json') {
      return JSON.stringify(logs, null, 2);
    }

    // CSV format
    const headers = [
      'ID', 'Event Type', 'Severity', 'Status', 'User ID', 'Username',
      'IP Address', 'Resource', 'Action', 'Description', 'Timestamp',
    ];

    const csvRows = [
      headers.join(','),
      ...logs.map(log => [
        log.id,
        log.eventType,
        log.severity,
        log.status,
        log.userId || '',
        log.username || '',
        log.ipAddress || '',
        log.resource,
        log.action,
        `"${log.description.replace(/"/g, '""')}"`,
        log.timestamp.toISOString(),
      ].join(',')),
    ];

    return csvRows.join('\n');
  }

  private getDefaultRetentionDays(eventType: AuditEventType): number {
    // Security and compliance events have longer retention
    if (this.isSecurityEvent(eventType) || this.isComplianceEvent(eventType)) {
      return 2555; // 7 years
    }

    // Regular events
    switch (eventType) {
      case AuditEventType.LOGIN:
      case AuditEventType.LOGOUT:
        return 90; // 3 months
      case AuditEventType.DATA_ACCESS:
      case AuditEventType.DATA_VIEW:
        return 365; // 1 year
      case AuditEventType.DATA_CREATE:
      case AuditEventType.DATA_UPDATE:
      case AuditEventType.DATA_DELETE:
        return 2555; // 7 years
      default:
        return 365; // 1 year
    }
  }

  private isSecurityEvent(eventType: AuditEventType): boolean {
    return [
      AuditEventType.LOGIN_FAILED,
      AuditEventType.ACCOUNT_LOCKED,
      AuditEventType.SECURITY_VIOLATION,
      AuditEventType.PERMISSION_DENIED,
      AuditEventType.UNAUTHORIZED_ACCESS,
      AuditEventType.SUSPICIOUS_ACTIVITY,
      AuditEventType.SECURITY_SCAN,
    ].includes(eventType);
  }

  private isComplianceEvent(eventType: AuditEventType): boolean {
    return [
      AuditEventType.CONSENT_GIVEN,
      AuditEventType.CONSENT_WITHDRAWN,
      AuditEventType.DATA_RETENTION_EXECUTED,
      AuditEventType.DATA_ANONYMIZED,
      AuditEventType.COMPLIANCE_REPORT_GENERATED,
    ].includes(eventType);
  }

  async searchLogs(searchTerm: string, startDate?: Date, endDate?: Date): Promise<AuditLog[]> {
    const query = this.auditLogRepository.createQueryBuilder('audit');

    query.where(
      '(audit.description ILIKE :searchTerm OR audit.resource ILIKE :searchTerm OR audit.action ILIKE :searchTerm)',
      { searchTerm: `%${searchTerm}%` }
    );

    if (startDate && endDate) {
      query.andWhere('audit.timestamp BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    return query.orderBy('audit.timestamp', 'DESC').limit(100).getMany();
  }

  async getAuditTrail(resource: string, resourceId: string): Promise<AuditLog[]> {
    return this.auditLogRepository.find({
      where: [
        { resource, details: { resourceId } },
        { resource, oldValues: { id: resourceId } },
        { resource, newValues: { id: resourceId } },
      ],
      order: {
        timestamp: 'ASC',
      },
    });
  }

  async getAuditLogs(query?: AuditLogQuery): Promise<AuditLog[]> {
    if (!query) {
      return this.auditLogRepository.find({
        order: { timestamp: 'DESC' },
        take: 100,
      });
    }
    return this.query(query);
  }

  async getAuditLogById(id: string): Promise<AuditLog | null> {
    return this.auditLogRepository.findOne({ where: { id } });
  }

  async createAuditLog(entry: AuditLogEntry): Promise<AuditLog> {
    return this.log(entry);
  }

  async cleanupOldLogs(retentionDays?: number): Promise<number> {
    if (retentionDays) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
      
      const expiredLogs = await this.auditLogRepository
        .createQueryBuilder('audit')
        .where('audit.timestamp < :cutoffDate', { cutoffDate })
        .getMany();

      if (expiredLogs.length === 0) {
        return 0;
      }

      const expiredIds = expiredLogs.map(log => log.id);
      await this.auditLogRepository.delete(expiredIds);

      this.logger.log(`Cleaned up ${expiredLogs.length} audit logs older than ${retentionDays} days`);
      return expiredLogs.length;
    }

    return this.cleanupExpiredLogs();
  }
}