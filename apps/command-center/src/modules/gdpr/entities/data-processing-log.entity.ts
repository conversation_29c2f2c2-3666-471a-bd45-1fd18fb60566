import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum ProcessingActivity {
  COLLECTION = 'collection',
  ACCESS = 'access',
  MODIFICATION = 'modification',
  DELETION = 'deletion',
  SHARING = 'sharing',
  TRANSFER = 'transfer',
  ANONYMIZATION = 'anonymization',
  PROFILING = 'profiling',
  AUTOMATED_DECISION = 'automated_decision',
}

export enum ProcessingPurpose {
  CONSENT_FULFILLMENT = 'consent_fulfillment',
  CONTRACT_PERFORMANCE = 'contract_performance',
  LEGAL_OBLIGATION = 'legal_obligation',
  VITAL_INTERESTS = 'vital_interests',
  PUBLIC_TASK = 'public_task',
  LEGITIMATE_INTERESTS = 'legitimate_interests',
  MARKETING = 'marketing',
  ANALYTICS = 'analytics',
  SECURITY = 'security',
  SYSTEM_ADMINISTRATION = 'system_administration',
}

export enum ProcessingResult {
  SUCCESS = 'success',
  PARTIAL_SUCCESS = 'partial_success',
  FAILURE = 'failure',
  UNAUTHORIZED = 'unauthorized',
  BLOCKED = 'blocked',
}

@Entity('data_processing_logs')
@Index(['userId', 'activity', 'timestamp'])
@Index(['dataSubject', 'activity'])
@Index(['processingPurpose', 'timestamp'])
@Index(['legalBasis', 'timestamp'])
@Index(['result', 'timestamp'])
export class DataProcessingLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: true })
  userId?: string; // User performing the action

  @Column({ type: 'varchar', length: 255 })
  dataSubject: string; // Subject of the data being processed

  @Column({
    type: 'enum',
    enum: ProcessingActivity,
  })
  activity: ProcessingActivity;

  @Column({
    type: 'enum',
    enum: ProcessingPurpose,
  })
  processingPurpose: ProcessingPurpose;

  @Column({ type: 'varchar', length: 255 })
  legalBasis: string; // Legal basis for processing

  @Column({ type: 'text' })
  dataCategories: string; // Categories of personal data processed

  @Column({ type: 'text', nullable: true })
  dataFields?: string; // Specific fields accessed/modified

  @Column({ type: 'varchar', length: 255 })
  systemComponent: string; // System component that performed the processing

  @Column({ type: 'varchar', length: 255, nullable: true })
  thirdParty?: string; // Third party involved (if any)

  @Column({ type: 'text', nullable: true })
  recipient?: string; // Recipient of the data (if shared/transferred)

  @Column({
    type: 'enum',
    enum: ProcessingResult,
    default: ProcessingResult.SUCCESS,
  })
  result: ProcessingResult;

  @Column({ type: 'text', nullable: true })
  resultDetails?: string; // Additional details about the result

  @Column({ type: 'inet', nullable: true })
  ipAddress?: string;

  @Column({ type: 'text', nullable: true })
  userAgent?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  sessionId?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  requestId?: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({ type: 'boolean', default: false })
  isAutomated: boolean; // Whether the processing was automated

  @Column({ type: 'boolean', default: false })
  hasUserConsent: boolean; // Whether user consent was obtained

  @Column({ type: 'varchar', length: 255, nullable: true })
  consentId?: string; // Reference to consent record

  @Column({ type: 'boolean', default: false })
  involvesSpecialCategories: boolean; // Special categories of personal data (Article 9)

  @Column({ type: 'varchar', length: 255, nullable: true })
  dataRetentionPolicyId?: string; // Reference to applicable retention policy

  // Additional properties for anonymization service
  @Column({ type: 'varchar', length: 255, nullable: true })
  entityType?: string; // Type of entity being processed

  @Column({ type: 'uuid', nullable: true })
  entityId?: string; // ID of entity being processed

  @Column({ type: 'jsonb', nullable: true })
  details?: Record<string, any>; // Detailed processing information

  @Column({ type: 'boolean', default: true })
  success: boolean; // Whether the processing was successful

  @Column({ type: 'varchar', length: 255, nullable: true })
  action?: string; // Alternative action field

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  timestamp: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  isCompliant(): boolean {
    // Check if processing meets GDPR requirements
    return this.hasValidLegalBasis() && this.hasUserConsent && this.result === ProcessingResult.SUCCESS;
  }

  hasValidLegalBasis(): boolean {
    const validBases = [
      'consent',
      'contract',
      'legal_obligation',
      'vital_interests',
      'public_task',
      'legitimate_interests',
    ];
    return validBases.includes(this.legalBasis);
  }

  requiresConsentUpdate(): boolean {
    return this.processingPurpose === ProcessingPurpose.MARKETING && !this.hasUserConsent;
  }

  isHighRisk(): boolean {
    return (
      this.involvesSpecialCategories ||
      this.activity === ProcessingActivity.AUTOMATED_DECISION ||
      this.activity === ProcessingActivity.PROFILING ||
      this.result === ProcessingResult.UNAUTHORIZED
    );
  }

  getAgeInDays(): number {
    const now = new Date();
    const timeDiff = now.getTime() - this.timestamp.getTime();
    return Math.floor(timeDiff / (1000 * 3600 * 24));
  }
}