import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum DataCategory {
  PERSONAL_DATA = 'personal_data',
  SENSITIVE_DATA = 'sensitive_data',
  BEHAVIORAL_DATA = 'behavioral_data',
  TECHNICAL_DATA = 'technical_data',
  COMMUNICATION_DATA = 'communication_data',
  FINANCIAL_DATA = 'financial_data',
  HEALTH_DATA = 'health_data',
  BIOMETRIC_DATA = 'biometric_data',
}

export enum RetentionAction {
  DELETE = 'delete',
  ANONYMIZE = 'anonymize',
  ARCHIVE = 'archive',
  TRANSFER = 'transfer',
}

export enum PolicyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  ARCHIVED = 'archived',
}

@Entity('data_retention_policies')
@Index(['dataCategory', 'status'])
@Index(['isActive'])
export class DataRetentionPolicy {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: DataCategory,
  })
  dataCategory: DataCategory;

  @Column({ type: 'varchar', length: 255 })
  dataType: string; // Specific data type (e.g., 'user_profiles', 'audit_logs')

  @Column({ type: 'varchar', length: 255, nullable: true })
  entityType?: string; // Type of entity this policy applies to

  @Column({ type: 'integer' })
  retentionPeriodDays: number;

  @Column({
    type: 'enum',
    enum: RetentionAction,
    default: RetentionAction.DELETE,
  })
  retentionAction: RetentionAction;

  @Column({
    type: 'enum',
    enum: PolicyStatus,
    default: PolicyStatus.ACTIVE,
  })
  status: PolicyStatus;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'jsonb', nullable: true })
  conditions?: Record<string, any>; // Additional conditions for retention

  @Column({ type: 'text', nullable: true })
  legalBasis?: string; // Legal basis for retention

  @Column({ type: 'varchar', length: 255, nullable: true })
  approvedBy?: string; // User ID who approved the policy

  @Column({ type: 'timestamp', nullable: true })
  approvedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  effectiveDate?: Date;

  @Column({ type: 'timestamp', nullable: true })
  expirationDate?: Date;

  @Column({ type: 'integer', default: 1 })
  version: number;

  @Column({ type: 'uuid', nullable: true })
  parentPolicyId?: string; // Reference to previous version

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  isEffective(): boolean {
    const now = new Date();
    
    if (!this.isActive || this.status !== PolicyStatus.ACTIVE) {
      return false;
    }

    if (this.effectiveDate && this.effectiveDate > now) {
      return false;
    }

    if (this.expirationDate && this.expirationDate < now) {
      return false;
    }

    return true;
  }

  getRetentionDeadline(dataCreatedAt: Date): Date {
    const deadline = new Date(dataCreatedAt);
    deadline.setDate(deadline.getDate() + this.retentionPeriodDays);
    return deadline;
  }

  isDataDue(dataCreatedAt: Date): boolean {
    const deadline = this.getRetentionDeadline(dataCreatedAt);
    return deadline <= new Date();
  }

  getDaysUntilExpiration(dataCreatedAt: Date): number {
    const deadline = this.getRetentionDeadline(dataCreatedAt);
    const now = new Date();
    const timeDiff = deadline.getTime() - now.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }
}