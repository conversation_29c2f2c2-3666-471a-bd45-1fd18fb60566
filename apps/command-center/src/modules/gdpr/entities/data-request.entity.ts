import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum DataRequestType {
  ACCESS = 'access', // Article 15 - Right of access
  RECTIFICATION = 'rectification', // Article 16 - Right to rectification
  ERASURE = 'erasure', // Article 17 - Right to erasure ('right to be forgotten')
  RESTRICT_PROCESSING = 'restrict_processing', // Article 18 - Right to restriction of processing
  PORTABILITY = 'portability', // Article 20 - Right to data portability
  OBJECTION = 'objection', // Article 21 - Right to object
}

export enum DataRequestStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export enum DataRequestPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('data_requests')
@Index(['userId', 'type', 'status'])
@Index(['status', 'createdAt'])
@Index(['dueDate'])
export class DataRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  userId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({
    type: 'enum',
    enum: DataRequestType,
  })
  type: DataRequestType;

  @Column({
    type: 'enum',
    enum: DataRequestStatus,
    default: DataRequestStatus.PENDING,
  })
  status: DataRequestStatus;

  @Column({
    type: 'enum',
    enum: DataRequestPriority,
    default: DataRequestPriority.MEDIUM,
  })
  priority: DataRequestPriority;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'jsonb', nullable: true })
  requestData?: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  responseData?: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  details?: Record<string, any>; // Additional request details

  @Column({ type: 'text', nullable: true })
  rejectionReason?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  assignedTo?: string; // User ID of assigned staff member

  @Column({ type: 'timestamp' })
  dueDate: Date; // Must be completed within 30 days (GDPR requirement)

  @Column({ type: 'timestamp', nullable: true })
  completedAt?: Date;

  @Column({ type: 'inet', nullable: true })
  ipAddress?: string;

  @Column({ type: 'text', nullable: true })
  userAgent?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  verificationMethod?: string; // How user identity was verified

  @Column({ type: 'boolean', default: false })
  isVerified: boolean;

  @Column({ type: 'timestamp', nullable: true })
  verifiedAt?: Date;

  @Column({ type: 'jsonb', nullable: true })
  auditTrail?: Array<{
    timestamp: Date;
    action: string;
    performedBy: string;
    details?: Record<string, any>;
  }>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  isOverdue(): boolean {
    return this.dueDate < new Date() && this.status !== DataRequestStatus.COMPLETED;
  }

  getDaysRemaining(): number {
    const now = new Date();
    const timeDiff = this.dueDate.getTime() - now.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  canBeProcessed(): boolean {
    return this.isVerified && this.status === DataRequestStatus.PENDING;
  }

  addAuditEntry(action: string, performedBy: string, details?: Record<string, any>): void {
    if (!this.auditTrail) {
      this.auditTrail = [];
    }

    this.auditTrail.push({
      timestamp: new Date(),
      action,
      performedBy,
      details,
    });
  }
}