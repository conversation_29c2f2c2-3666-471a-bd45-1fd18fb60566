import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Cron } from '@nestjs/schedule';

import { DataRetentionPolicy, DataCategory, RetentionAction, PolicyStatus } from '../entities/data-retention-policy.entity';
import { DataProcessingLog, ProcessingActivity, ProcessingPurpose, ProcessingResult } from '../entities/data-processing-log.entity';

export interface RetentionPolicyConfig {
  name: string;
  description: string;
  dataCategory: DataCategory;
  dataType: string;
  retentionPeriodDays: number;
  retentionAction: RetentionAction;
  legalBasis?: string;
  conditions?: Record<string, any>;
}

@Injectable()
export class DataRetentionService {
  private readonly logger = new Logger(DataRetentionService.name);

  constructor(
    @InjectRepository(DataRetentionPolicy)
    private readonly retentionPolicyRepository: Repository<DataRetentionPolicy>,
    @InjectRepository(DataProcessingLog)
    private readonly processingLogRepository: Repository<DataProcessingLog>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Create data retention policy
   */
  async createRetentionPolicy(config: RetentionPolicyConfig): Promise<DataRetentionPolicy> {
    this.logger.log(`Creating retention policy: ${config.name}`);

    const policy = this.retentionPolicyRepository.create({
      name: config.name,
      description: config.description,
      dataCategory: config.dataCategory,
      dataType: config.dataType,
      retentionPeriodDays: config.retentionPeriodDays,
      retentionAction: config.retentionAction,
      status: PolicyStatus.ACTIVE,
      legalBasis: config.legalBasis || 'GDPR Article 5(1)(e) - Storage limitation',
      conditions: config.conditions || {},
      isActive: true,
      effectiveDate: new Date(),
    });

    return this.retentionPolicyRepository.save(policy);
  }

  /**
   * Get retention policies
   */
  async getRetentionPolicies(dataCategory?: DataCategory | string): Promise<DataRetentionPolicy[]> {
    const queryBuilder = this.retentionPolicyRepository.createQueryBuilder('policy')
      .where('policy.status = :status', { status: PolicyStatus.ACTIVE });

    if (dataCategory) {
      queryBuilder.andWhere('policy.dataCategory = :dataCategory', { dataCategory });
    }

    return queryBuilder.orderBy('policy.createdAt', 'DESC').getMany();
  }

  /**
   * Update retention policy
   */
  async updateRetentionPolicy(id: string, updates: Partial<RetentionPolicyConfig>): Promise<DataRetentionPolicy | null> {
    const policy = await this.retentionPolicyRepository.findOne({ where: { id } });
    
    if (!policy) {
      return null;
    }

    Object.assign(policy, updates);
    policy.updatedAt = new Date();

    return this.retentionPolicyRepository.save(policy);
  }

  /**
   * Delete retention policy
   */
  async deleteRetentionPolicy(id: string): Promise<boolean> {
    const policy = await this.retentionPolicyRepository.findOne({ where: { id } });
    
    if (!policy) {
      return false;
    }

    policy.isActive = false;
    policy.updatedAt = new Date();
    
    await this.retentionPolicyRepository.save(policy);
    return true;
  }

  /**
   * Check data for retention compliance
   */
  async checkRetentionCompliance(entityType: string, entityId: string): Promise<{
    isCompliant: boolean;
    policy?: DataRetentionPolicy;
    daysUntilExpiry?: number;
    shouldDelete: boolean;
    reason?: string;
  }> {
    const policies = await this.getRetentionPolicies(entityType as DataCategory);
    
    if (policies.length === 0) {
      return {
        isCompliant: true,
        shouldDelete: false,
        reason: 'No retention policy defined',
      };
    }

    const policy = policies[0]; // Use the first active policy
    
    // Get the entity creation date (this would need to be implemented based on your data model)
    const entityCreationDate = await this.getEntityCreationDate(entityType, entityId);
    
    if (!entityCreationDate) {
      return {
        isCompliant: false,
        policy,
        shouldDelete: false,
        reason: 'Cannot determine entity creation date',
      };
    }

    const daysSinceCreation = Math.floor(
      (Date.now() - entityCreationDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    const daysUntilExpiry = policy.retentionPeriodDays - daysSinceCreation;
    const shouldDelete = daysUntilExpiry <= 0;

    return {
      isCompliant: !shouldDelete,
      policy,
      daysUntilExpiry,
      shouldDelete,
      reason: shouldDelete ? 'Retention period exceeded' : 'Within retention period',
    };
  }

  /**
   * Execute data retention
   */
  async executeDataRetention(entityType: string, entityId: string): Promise<{
    success: boolean;
    method: string;
    timestamp: Date;
    error?: string;
  }> {
    this.logger.log(`Executing data retention for ${entityType}:${entityId}`);

    try {
      const compliance = await this.checkRetentionCompliance(entityType, entityId);
      
      if (!compliance.shouldDelete) {
        return {
          success: false,
          method: 'none',
          timestamp: new Date(),
          error: 'Entity not eligible for deletion',
        };
      }

      const method = compliance.policy?.retentionAction || RetentionAction.ANONYMIZE;
      
      // Execute the deletion method
      await this.executeRetentionMethod(entityType, entityId, method);

      // Log the retention action
      await this.logRetentionAction(entityType, entityId, method);

      return {
        success: true,
        method,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to execute data retention for ${entityType}:${entityId}:`, error);
      return {
        success: false,
        method: 'error',
        timestamp: new Date(),
        error: error.message,
      };
    }
  }

  /**
   * Automated retention cleanup
   */
  @Cron('0 2 * * *') // Daily at 2 AM
  async automatedRetentionCleanup(): Promise<void> {
    this.logger.log('Starting automated retention cleanup');

    try {
      const policies = await this.getRetentionPolicies();
      
      for (const policy of policies) {
        await this.processRetentionForPolicy(policy);
      }

      this.logger.log('Automated retention cleanup completed');
    } catch (error) {
      this.logger.error('Failed to execute automated retention cleanup:', error);
    }
  }

  /**
   * Get retention statistics
   */
  async getRetentionStatistics(): Promise<{
    totalPolicies: number;
    activePolicies: number;
    entitiesProcessedToday: number;
    entitiesPendingDeletion: number;
    retentionByMethod: Record<string, number>;
  }> {
    const totalPolicies = await this.retentionPolicyRepository.count();
    const activePolicies = await this.retentionPolicyRepository.count({
      where: { status: PolicyStatus.ACTIVE },
    });

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const qb = this.processingLogRepository.createQueryBuilder('log');
    const todayLogs = await qb
      .where('log.activity = :activity', { activity: ProcessingActivity.DELETION })
      .andWhere('DATE(log.timestamp) = DATE(:today)', { today })
      .getCount();

    return {
      totalPolicies,
      activePolicies,
      entitiesProcessedToday: todayLogs,
      entitiesPendingDeletion: 0, // Would need to implement based on actual data
      retentionByMethod: {
        hard_delete: 0,
        soft_delete: 0,
        anonymize: 0,
      },
    };
  }

  private async getEntityCreationDate(entityType: string, entityId: string): Promise<Date | null> {
    // This is a placeholder - in a real implementation, you would query the appropriate
    // table based on entityType to get the creation date
    // For now, return a mock date
    return new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
  }

  private async executeRetentionMethod(entityType: string, entityId: string, method: string): Promise<void> {
    // This is a placeholder - in a real implementation, you would execute the actual
    // deletion/anonymization based on the entity type and method
    
    switch (method) {
      case 'hard_delete':
        this.logger.log(`Hard deleting ${entityType}:${entityId}`);
        break;
      case 'soft_delete':
        this.logger.log(`Soft deleting ${entityType}:${entityId}`);
        break;
      case 'anonymize':
        this.logger.log(`Anonymizing ${entityType}:${entityId}`);
        break;
    }
  }

  private async logRetentionAction(entityType: string, entityId: string, method: string): Promise<void> {
    const log = this.processingLogRepository.create({
      userId: 'system',
      dataSubject: entityId,
      activity: ProcessingActivity.DELETION,
      processingPurpose: ProcessingPurpose.LEGAL_OBLIGATION,
      legalBasis: 'GDPR Article 5(1)(e) - Storage limitation',
      dataCategories: entityType,
      systemComponent: 'gdpr-retention-service',
      result: ProcessingResult.SUCCESS,
      isAutomated: true,
      hasUserConsent: false,
      involvesSpecialCategories: false,
      metadata: { method },
      timestamp: new Date(),
    });

    await this.processingLogRepository.save(log);
  }

  private async processRetentionForPolicy(policy: DataRetentionPolicy): Promise<void> {
    this.logger.log(`Processing retention for policy: ${policy.name}`);

    // This would need to be implemented based on your specific data model
    // You would query for entities of the specified type that exceed the retention period
    
    // Placeholder implementation
    const expiredEntities = await this.findExpiredEntities(policy);
    
    for (const entity of expiredEntities) {
      await this.executeDataRetention(policy.dataCategory, entity.id);
    }
  }

  private async findExpiredEntities(policy: DataRetentionPolicy): Promise<Array<{ id: string }>> {
    // Placeholder - would implement actual query based on entity type
    return [];
  }
}