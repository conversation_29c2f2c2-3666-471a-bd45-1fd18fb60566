import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { ConsentRecord, ConsentType, ConsentStatus, LegalBasis } from '../entities/consent-record.entity';

export interface ConsentRequest {
  userId: string;
  consentType: ConsentType;
  purpose: string;
  description?: string;
  legalBasis?: LegalBasis;
  expirationDate?: Date;
  source: string;
  ipAddress?: string;
  userAgent?: string;
  version?: string;
  isDoubleOptIn?: boolean;
}

export interface ConsentUpdateRequest {
  status: ConsentStatus;
  withdrawalReason?: string;
  ipAddress?: string;
  userAgent?: string;
}

@Injectable()
export class ConsentService {
  private readonly logger = new Logger(ConsentService.name);

  constructor(
    @InjectRepository(ConsentRecord)
    private readonly consentRepository: Repository<ConsentRecord>,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async giveConsent(request: ConsentRequest): Promise<ConsentRecord> {
    this.logger.log(`Processing consent request for user ${request.userId}, type: ${request.consentType}`);

    // Check if there's already a valid consent
    const existingConsent = await this.findLatestConsent(request.userId, request.consentType);
    
    if (existingConsent && existingConsent.isValid()) {
      this.logger.warn(`User ${request.userId} already has valid consent for ${request.consentType}`);
      return existingConsent;
    }

    const consent = this.consentRepository.create({
      userId: request.userId,
      consentType: request.consentType,
      status: ConsentStatus.GIVEN,
      legalBasis: request.legalBasis || LegalBasis.CONSENT,
      purpose: request.purpose,
      description: request.description,
      source: request.source,
      ipAddress: request.ipAddress,
      userAgent: request.userAgent,
      expirationDate: request.expirationDate,
      version: request.version,
      isDoubleOptIn: request.isDoubleOptIn || false,
    });

    const savedConsent = await this.consentRepository.save(consent);

    // Emit event for consent given
    this.eventEmitter.emit('consent.given', {
      userId: request.userId,
      consentType: request.consentType,
      consentId: savedConsent.id,
      timestamp: new Date(),
    });

    this.logger.log(`Consent given for user ${request.userId}, consent ID: ${savedConsent.id}`);
    return savedConsent;
  }

  async withdrawConsent(
    userId: string,
    consentType: ConsentType,
    updateRequest: ConsentUpdateRequest
  ): Promise<ConsentRecord> {
    this.logger.log(`Processing consent withdrawal for user ${userId}, type: ${consentType}`);

    const consent = await this.findLatestConsent(userId, consentType);
    
    if (!consent) {
      throw new Error(`No consent found for user ${userId} and type ${consentType}`);
    }

    if (!consent.canBeWithdrawn()) {
      throw new Error(`Consent cannot be withdrawn for user ${userId} and type ${consentType}`);
    }

    consent.status = ConsentStatus.WITHDRAWN;
    consent.withdrawnAt = new Date();
    consent.withdrawalReason = updateRequest.withdrawalReason;

    const updatedConsent = await this.consentRepository.save(consent);

    // Emit event for consent withdrawal
    this.eventEmitter.emit('consent.withdrawn', {
      userId,
      consentType,
      consentId: consent.id,
      withdrawalReason: updateRequest.withdrawalReason,
      timestamp: new Date(),
    });

    this.logger.log(`Consent withdrawn for user ${userId}, consent ID: ${consent.id}`);
    return updatedConsent;
  }

  async confirmDoubleOptIn(consentId: string): Promise<ConsentRecord> {
    this.logger.log(`Confirming double opt-in for consent ${consentId}`);

    const consent = await this.consentRepository.findOne({
      where: { id: consentId },
    });

    if (!consent) {
      throw new Error(`Consent ${consentId} not found`);
    }

    if (!consent.isDoubleOptIn) {
      throw new Error(`Consent ${consentId} is not configured for double opt-in`);
    }

    if (consent.doubleOptInConfirmedAt) {
      throw new Error(`Consent ${consentId} already confirmed`);
    }

    consent.doubleOptInConfirmedAt = new Date();
    const updatedConsent = await this.consentRepository.save(consent);

    // Emit event for double opt-in confirmation
    this.eventEmitter.emit('consent.doubleOptIn.confirmed', {
      userId: consent.userId,
      consentType: consent.consentType,
      consentId: consent.id,
      timestamp: new Date(),
    });

    this.logger.log(`Double opt-in confirmed for consent ${consentId}`);
    return updatedConsent;
  }

  async hasValidConsent(userId: string, consentType: ConsentType | string): Promise<boolean> {
    const consent = await this.findLatestConsent(userId, consentType as ConsentType);
    return consent ? consent.isValid() : false;
  }

  async getUserConsents(userId: string): Promise<ConsentRecord[]> {
    return this.consentRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async getUserConsentsByType(userId: string, consentType: ConsentType): Promise<ConsentRecord[]> {
    return this.consentRepository.find({
      where: { userId, consentType },
      order: { createdAt: 'DESC' },
    });
  }

  async getConsentHistory(userId: string, consentType: ConsentType): Promise<ConsentRecord[]> {
    return this.consentRepository.find({
      where: { userId, consentType },
      order: { createdAt: 'ASC' },
    });
  }

  async findLatestConsent(userId: string, consentType: ConsentType): Promise<ConsentRecord | null> {
    return this.consentRepository.findOne({
      where: { userId, consentType },
      order: { createdAt: 'DESC' },
    });
  }

  async getConsentSummary(userId: string): Promise<Record<ConsentType, boolean>> {
    const consents = await this.getUserConsents(userId);
    const summary: Record<ConsentType, boolean> = {} as any;

    // Initialize all consent types to false
    Object.values(ConsentType).forEach(type => {
      summary[type] = false;
    });

    // Check latest consent for each type
    for (const type of Object.values(ConsentType)) {
      const latestConsent = consents.find(c => c.consentType === type);
      summary[type] = latestConsent ? latestConsent.isValid() : false;
    }

    return summary;
  }

  async getExpiringConsents(daysBefore: number = 30): Promise<ConsentRecord[]> {
    const expirationDate = new Date();
    expirationDate.setDate(expirationDate.getDate() + daysBefore);

    return this.consentRepository
      .createQueryBuilder('consent')
      .where('consent.status = :status', { status: ConsentStatus.GIVEN })
      .andWhere('consent.expirationDate IS NOT NULL')
      .andWhere('consent.expirationDate <= :expirationDate', { expirationDate })
      .andWhere('consent.expirationDate > :now', { now: new Date() })
      .orderBy('consent.expirationDate', 'ASC')
      .getMany();
  }

  async markExpiredConsents(): Promise<number> {
    const result = await this.consentRepository
      .createQueryBuilder()
      .update(ConsentRecord)
      .set({ status: ConsentStatus.EXPIRED })
      .where('status = :status', { status: ConsentStatus.GIVEN })
      .andWhere('expirationDate < :now', { now: new Date() })
      .execute();

    this.logger.log(`Marked ${result.affected} consents as expired`);
    return result.affected || 0;
  }

  async getConsentMetrics(startDate: Date, endDate: Date): Promise<any> {
    const consents = await this.consentRepository
      .createQueryBuilder('consent')
      .where('consent.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getMany();

    const metrics = {
      totalConsents: consents.length,
      consentsByType: {} as Record<ConsentType, number>,
      consentsByStatus: {} as Record<ConsentStatus, number>,
      consentsBySource: {} as Record<string, number>,
      averageConsentDuration: 0,
      doubleOptInRate: 0,
      withdrawalRate: 0,
    };

    // Initialize counters
    Object.values(ConsentType).forEach(type => {
      metrics.consentsByType[type] = 0;
    });
    Object.values(ConsentStatus).forEach(status => {
      metrics.consentsByStatus[status] = 0;
    });

    let totalDuration = 0;
    let durationCount = 0;
    let doubleOptInCount = 0;
    let withdrawnCount = 0;

    consents.forEach(consent => {
      // Count by type
      metrics.consentsByType[consent.consentType]++;
      
      // Count by status
      metrics.consentsByStatus[consent.status]++;
      
      // Count by source
      metrics.consentsBySource[consent.source] = (metrics.consentsBySource[consent.source] || 0) + 1;
      
      // Calculate duration for withdrawn consents
      if (consent.withdrawnAt) {
        const duration = consent.withdrawnAt.getTime() - consent.createdAt.getTime();
        totalDuration += duration;
        durationCount++;
        withdrawnCount++;
      }
      
      // Count double opt-in
      if (consent.isDoubleOptIn) {
        doubleOptInCount++;
      }
    });

    metrics.averageConsentDuration = durationCount > 0 ? totalDuration / durationCount : 0;
    metrics.doubleOptInRate = consents.length > 0 ? (doubleOptInCount / consents.length) * 100 : 0;
    metrics.withdrawalRate = consents.length > 0 ? (withdrawnCount / consents.length) * 100 : 0;

    return metrics;
  }

  async validateConsentForProcessing(
    userId: string,
    consentType: ConsentType,
    purpose: string
  ): Promise<boolean> {
    const consent = await this.findLatestConsent(userId, consentType);
    
    if (!consent || !consent.isValid()) {
      return false;
    }

    // Check if the purpose matches
    if (consent.purpose !== purpose) {
      this.logger.warn(`Consent purpose mismatch for user ${userId}: expected ${purpose}, got ${consent.purpose}`);
      return false;
    }

    return true;
  }

  async renewConsent(userId: string, consentType: ConsentType, newExpirationDate: Date): Promise<ConsentRecord> {
    const consent = await this.findLatestConsent(userId, consentType);
    
    if (!consent) {
      throw new Error(`No consent found for user ${userId} and type ${consentType}`);
    }

    // Create new consent record for renewal
    const renewedConsent = this.consentRepository.create({
      userId: consent.userId,
      consentType: consent.consentType,
      status: ConsentStatus.GIVEN,
      legalBasis: consent.legalBasis,
      purpose: consent.purpose,
      description: consent.description,
      source: 'renewal',
      expirationDate: newExpirationDate,
      version: consent.version,
      isDoubleOptIn: consent.isDoubleOptIn,
    });

    const savedConsent = await this.consentRepository.save(renewedConsent);

    // Emit event for consent renewal
    this.eventEmitter.emit('consent.renewed', {
      userId,
      consentType,
      oldConsentId: consent.id,
      newConsentId: savedConsent.id,
      timestamp: new Date(),
    });

    this.logger.log(`Consent renewed for user ${userId}, new consent ID: ${savedConsent.id}`);
    return savedConsent;
  }

  async bulkWithdrawConsents(userId: string, consentTypes: ConsentType[]): Promise<ConsentRecord[]> {
    const results: ConsentRecord[] = [];
    
    for (const consentType of consentTypes) {
      try {
        const withdrawnConsent = await this.withdrawConsent(userId, consentType, {
          status: ConsentStatus.WITHDRAWN,
          withdrawalReason: 'Bulk withdrawal requested by user',
        });
        results.push(withdrawnConsent);
      } catch (error) {
        this.logger.error(`Failed to withdraw consent ${consentType} for user ${userId}:`, error);
      }
    }

    return results;
  }

  async recordConsent(request: ConsentRequest): Promise<ConsentRecord> {
    return this.giveConsent(request);
  }

  async getConsentRecords(userId?: string, consentType?: ConsentType): Promise<ConsentRecord[]> {
    const whereClause: any = {};
    if (userId) whereClause.userId = userId;
    if (consentType) whereClause.consentType = consentType;

    return this.consentRepository.find({
      where: whereClause,
      order: { createdAt: 'DESC' },
    });
  }

  async getConsentById(id: string): Promise<ConsentRecord | null> {
    return this.consentRepository.findOne({ where: { id } });
  }

  async updateConsent(id: string, updateRequest: ConsentUpdateRequest): Promise<ConsentRecord> {
    const consent = await this.consentRepository.findOne({ where: { id } });
    if (!consent) {
      throw new Error(`Consent ${id} not found`);
    }

    if (updateRequest.status === ConsentStatus.WITHDRAWN) {
      consent.status = ConsentStatus.WITHDRAWN;
      consent.withdrawnAt = new Date();
      consent.withdrawalReason = updateRequest.withdrawalReason;
    } else {
      consent.status = updateRequest.status;
    }

    return this.consentRepository.save(consent);
  }

  async withdrawUserConsents(userId: string, reason?: string): Promise<ConsentRecord[]> {
    const activeConsents = await this.consentRepository.find({
      where: {
        userId,
        status: ConsentStatus.GIVEN,
      },
    });

    const results: ConsentRecord[] = [];
    for (const consent of activeConsents) {
      consent.status = ConsentStatus.WITHDRAWN;
      consent.withdrawnAt = new Date();
      consent.withdrawalReason = reason || 'User requested withdrawal of all consents';
      
      const updated = await this.consentRepository.save(consent);
      results.push(updated);
    }

    return results;
  }

  async getUserConsentStatus(userId: string): Promise<Record<ConsentType, boolean>> {
    return this.getConsentSummary(userId);
  }

  async getConsentPurposes(): Promise<string[]> {
    const consents = await this.consentRepository
      .createQueryBuilder('consent')
      .select('DISTINCT consent.purpose', 'purpose')
      .getRawMany();

    return consents.map(c => c.purpose).filter(Boolean);
  }

  async bulkRecordConsents(requests: ConsentRequest[]): Promise<ConsentRecord[]> {
    const results: ConsentRecord[] = [];
    
    for (const request of requests) {
      try {
        const consent = await this.recordConsent(request);
        results.push(consent);
      } catch (error) {
        this.logger.error(`Failed to record consent for user ${request.userId}:`, error);
      }
    }

    return results;
  }

  async getConsentAnalytics(startDate: Date, endDate: Date): Promise<any> {
    return this.getConsentMetrics(startDate, endDate);
  }

  async generateConsentReport(userId?: string, startDate?: Date, endDate?: Date): Promise<any> {
    const whereClause: any = {};
    if (userId) whereClause.userId = userId;
    if (startDate && endDate) {
      whereClause.createdAt = {
        $gte: startDate,
        $lte: endDate,
      };
    }

    const consents = await this.consentRepository.find({
      where: whereClause,
      order: { createdAt: 'DESC' },
    });

    const report = {
      totalConsents: consents.length,
      consentsByType: {} as Record<string, number>,
      consentsByStatus: {} as Record<string, number>,
      consents: consents,
      generatedAt: new Date(),
    };

    // Count by type and status
    consents.forEach(consent => {
      report.consentsByType[consent.consentType] = (report.consentsByType[consent.consentType] || 0) + 1;
      report.consentsByStatus[consent.status] = (report.consentsByStatus[consent.status] || 0) + 1;
    });

    return report;
  }

  async verifyConsent(userId: string, consentType: ConsentType, purpose: string): Promise<boolean> {
    return this.validateConsentForProcessing(userId, consentType, purpose);
  }

  async getConsentAuditTrail(consentId: string): Promise<any[]> {
    const consent = await this.consentRepository.findOne({ where: { id: consentId } });
    if (!consent) {
      throw new Error(`Consent ${consentId} not found`);
    }

    // Return audit trail (simplified implementation)
    const auditTrail = [{
      action: 'consent_created',
      timestamp: consent.createdAt,
      details: {
        consentType: consent.consentType,
        status: consent.status,
        source: consent.source,
      },
    }];

    if (consent.withdrawnAt) {
      auditTrail.push({
        action: 'consent_withdrawn',
        timestamp: consent.withdrawnAt,
        details: {
          consentType: consent.consentType,
          status: consent.status,
          source: consent.source,
          ...(consent.withdrawalReason && { withdrawalReason: consent.withdrawalReason }),
        },
      });
    }

    return auditTrail;
  }
}