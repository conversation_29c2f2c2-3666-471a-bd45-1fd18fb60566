import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { DataRequest, DataRequestType, DataRequestStatus } from '../entities/data-request.entity';
import { DataProcessingLog } from '../entities/data-processing-log.entity';

export interface DataExportRequest {
  userId: string;
  requestedBy: string;
  exportFormat: 'json' | 'csv' | 'xml' | 'pdf';
  dataTypes: string[];
  includeMetadata?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface ExportedData {
  userId: string;
  exportDate: Date;
  format: string;
  dataTypes: string[];
  data: Record<string, any>;
  metadata?: {
    totalRecords: number;
    exportDurationMs: number;
    fileSize: number;
  };
}

@Injectable()
export class DataPortabilityService {
  private readonly logger = new Logger(DataPortabilityService.name);

  constructor(
    @InjectRepository(DataRequest)
    private readonly dataRequestRepository: Repository<DataRequest>,
    @InjectRepository(DataProcessingLog)
    private readonly processingLogRepository: Repository<DataProcessingLog>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Create data export request
   */
  async createExportRequest(request: DataExportRequest): Promise<DataRequest> {
    this.logger.log(`Creating data export request for user ${request.userId}`);

    const dataRequest = this.dataRequestRepository.create({
      userId: request.userId,
      type: DataRequestType.PORTABILITY,
      status: DataRequestStatus.PENDING,
      description: `Export data in ${request.exportFormat} format`,
      requestData: {
        exportFormat: request.exportFormat,
        dataTypes: request.dataTypes,
        includeMetadata: request.includeMetadata,
        dateRange: request.dateRange,
        requestedBy: request.requestedBy,
      },
      createdAt: new Date(),
    });

    const savedRequest = await this.dataRequestRepository.save(dataRequest);

    // Process the export request asynchronously
    if (Array.isArray(savedRequest)) {
      this.processExportRequest(savedRequest[0].id);
      return savedRequest[0];
    } else {
      this.processExportRequest(savedRequest.id);
      return savedRequest;
    }
  }

  /**
   * Get export requests
   */
  async getExportRequests(userId?: string, status?: string): Promise<DataRequest[]> {
    const queryBuilder = this.dataRequestRepository.createQueryBuilder('request')
      .where('request.type = :type', { type: DataRequestType.PORTABILITY });

    if (userId) {
      queryBuilder.andWhere('request.userId = :userId', { userId });
    }

    if (status) {
      queryBuilder.andWhere('request.status = :status', { status });
    }

    return queryBuilder.orderBy('request.createdAt', 'DESC').getMany();
  }

  /**
   * Get export request by ID
   */
  async getExportRequestById(id: string): Promise<DataRequest | null> {
    return this.dataRequestRepository.findOne({ 
      where: { id, type: DataRequestType.PORTABILITY } 
    });
  }

  /**
   * Export user data
   */
  async exportUserData(userId: string, options: {
    format: 'json' | 'csv' | 'xml' | 'pdf';
    dataTypes: string[];
    includeMetadata?: boolean;
    dateRange?: { start: Date; end: Date };
  }): Promise<ExportedData> {
    this.logger.log(`Exporting data for user ${userId}`);

    const startTime = Date.now();
    const exportedData: Record<string, any> = {};

    // Export data by type
    for (const dataType of options.dataTypes) {
      exportedData[dataType] = await this.exportDataType(userId, dataType, options.dateRange);
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    // Format the data according to requested format
    const formattedData = await this.formatExportData(exportedData, options.format);

    const result: ExportedData = {
      userId,
      exportDate: new Date(),
      format: options.format,
      dataTypes: options.dataTypes,
      data: formattedData,
    };

    if (options.includeMetadata) {
      result.metadata = {
        totalRecords: this.countTotalRecords(exportedData),
        exportDurationMs: duration,
        fileSize: JSON.stringify(formattedData).length,
      };
    }

    // Log the export action
    await this.logDataAction(userId, 'data_export', {
      format: options.format,
      dataTypes: options.dataTypes,
      recordCount: result.metadata?.totalRecords,
      duration,
    });

    return result;
  }

  /**
   * Generate data portability report
   */
  async generatePortabilityReport(userId: string): Promise<{
    userId: string;
    reportDate: Date;
    dataCategories: Array<{
      category: string;
      description: string;
      recordCount: number;
      lastUpdated: Date;
      canExport: boolean;
    }>;
    exportHistory: Array<{
      exportDate: Date;
      format: string;
      dataTypes: string[];
      status: string;
    }>;
    retentionInfo: Array<{
      dataType: string;
      retentionPeriod: string;
      expiryDate?: Date;
    }>;
  }> {
    this.logger.log(`Generating portability report for user ${userId}`);

    // Get data categories available for the user
    const dataCategories = await this.getUserDataCategories(userId);

    // Get export history
    const exportHistory = await this.getExportRequests(userId);

    // Get retention information
    const retentionInfo = await this.getRetentionInfo(userId);

    return {
      userId,
      reportDate: new Date(),
      dataCategories,
      exportHistory: exportHistory.map(request => ({
        exportDate: request.createdAt,
        format: request.details?.exportFormat || 'json',
        dataTypes: request.details?.dataTypes || [],
        status: request.status,
      })),
      retentionInfo,
    };
  }

  /**
   * Validate export request
   */
  async validateExportRequest(request: DataExportRequest): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate user exists
    if (!request.userId) {
      errors.push('User ID is required');
    }

    // Validate export format
    const supportedFormats = ['json', 'csv', 'xml', 'pdf'];
    if (!supportedFormats.includes(request.exportFormat)) {
      errors.push('Unsupported export format');
    }

    // Validate data types
    if (!request.dataTypes || request.dataTypes.length === 0) {
      errors.push('At least one data type must be specified');
    }

    // Check for large exports
    if (request.dataTypes.length > 10) {
      warnings.push('Large export may take significant time to process');
    }

    // Validate date range
    if (request.dateRange) {
      if (request.dateRange.start > request.dateRange.end) {
        errors.push('Start date must be before end date');
      }

      const yearAgo = new Date();
      yearAgo.setFullYear(yearAgo.getFullYear() - 1);
      
      if (request.dateRange.start < yearAgo) {
        warnings.push('Export includes data older than 1 year');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private async processExportRequest(requestId: string): Promise<void> {
    try {
      const request = await this.dataRequestRepository.findOne({ where: { id: requestId } });
      if (!request) return;

      // Update status to processing
      request.status = DataRequestStatus.IN_PROGRESS;
      request.updatedAt = new Date();
      await this.dataRequestRepository.save(request);

      // Export the data
      const exportOptions = request.requestData || {};
      const exportResult = await this.exportUserData(request.userId, {
        format: exportOptions.exportFormat || 'json',
        dataTypes: exportOptions.dataTypes || ['profile', 'activities', 'preferences'],
        includeMetadata: exportOptions.includeMetadata || false,
        dateRange: exportOptions.dateRange,
      });

      // Update request with results
      request.status = DataRequestStatus.COMPLETED;
      request.completedAt = new Date();
      request.responseData = exportResult;
      request.updatedAt = new Date();

      await this.dataRequestRepository.save(request);

      this.logger.log(`Export request ${requestId} completed successfully`);
    } catch (error) {
      this.logger.error(`Failed to process export request ${requestId}:`, error);

      // Update request status to failed
      const request = await this.dataRequestRepository.findOne({ where: { id: requestId } });
      if (request) {
        request.status = DataRequestStatus.REJECTED;
        request.rejectionReason = error.message;
        request.updatedAt = new Date();
        await this.dataRequestRepository.save(request);
      }
    }
  }

  private async exportDataType(userId: string, dataType: string, dateRange?: { start: Date; end: Date }): Promise<any[]> {
    // This is a placeholder - in a real implementation, you would query the appropriate
    // tables based on the dataType to export user data
    
    const mockData: Record<string, any[]> = {
      profile: [
        { id: userId, name: 'User Name', email: '<EMAIL>', createdAt: new Date() }
      ],
      activities: [
        { id: '1', action: 'login', timestamp: new Date(), userId }
      ],
      preferences: [
        { id: '1', setting: 'theme', value: 'dark', userId }
      ],
      communications: [
        { id: '1', type: 'email', content: 'Welcome message', sentAt: new Date(), userId }
      ],
    };

    return mockData[dataType] || [];
  }

  private async formatExportData(data: Record<string, any>, format: string): Promise<any> {
    switch (format) {
      case 'json':
        return data;
      case 'csv':
        return this.convertToCSV(data);
      case 'xml':
        return this.convertToXML(data);
      case 'pdf':
        return this.convertToPDF(data);
      default:
        return data;
    }
  }

  private convertToCSV(data: Record<string, any>): string {
    // Simple CSV conversion - in a real implementation, you'd use a proper CSV library
    let csv = '';
    for (const [dataType, records] of Object.entries(data)) {
      csv += `\n--- ${dataType.toUpperCase()} ---\n`;
      if (records.length > 0) {
        const headers = Object.keys(records[0]);
        csv += headers.join(',') + '\n';
        for (const record of records) {
          csv += headers.map(h => record[h]).join(',') + '\n';
        }
      }
    }
    return csv;
  }

  private convertToXML(data: Record<string, any>): string {
    // Simple XML conversion
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<userData>\n';
    for (const [dataType, records] of Object.entries(data)) {
      xml += `  <${dataType}>\n`;
      for (const record of records) {
        xml += '    <record>\n';
        for (const [key, value] of Object.entries(record)) {
          xml += `      <${key}>${value}</${key}>\n`;
        }
        xml += '    </record>\n';
      }
      xml += `  </${dataType}>\n`;
    }
    xml += '</userData>';
    return xml;
  }

  private convertToPDF(data: Record<string, any>): string {
    // Placeholder for PDF conversion - would use a library like puppeteer or pdfkit
    return JSON.stringify(data, null, 2);
  }

  private countTotalRecords(data: Record<string, any[]>): number {
    return Object.values(data).reduce((total, records) => total + records.length, 0);
  }

  private async getUserDataCategories(userId: string): Promise<Array<{
    category: string;
    description: string;
    recordCount: number;
    lastUpdated: Date;
    canExport: boolean;
  }>> {
    // Mock implementation
    return [
      {
        category: 'profile',
        description: 'Basic profile information',
        recordCount: 1,
        lastUpdated: new Date(),
        canExport: true,
      },
      {
        category: 'activities',
        description: 'User activity logs',
        recordCount: 100,
        lastUpdated: new Date(),
        canExport: true,
      },
    ];
  }

  private async getRetentionInfo(userId: string): Promise<Array<{
    dataType: string;
    retentionPeriod: string;
    expiryDate?: Date;
  }>> {
    // Mock implementation
    return [
      {
        dataType: 'activities',
        retentionPeriod: '2 years',
        expiryDate: new Date(Date.now() + 2 * 365 * 24 * 60 * 60 * 1000),
      },
    ];
  }

  private async logDataAction(userId: string, action: string, details: any): Promise<void> {
    const log = this.processingLogRepository.create({
      entityType: 'user',
      entityId: userId,
      action,
      details,
      timestamp: new Date(),
      success: true,
    });

    await this.processingLogRepository.save(log);
  }
}