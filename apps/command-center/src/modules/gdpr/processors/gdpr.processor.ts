import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';

import { GdprService } from '../services/gdpr.service';
import { DataPortabilityService } from '../services/data-portability.service';
import { DataAnonymizationService } from '../services/data-anonymization.service';
import { DataRequestStatus } from '../entities/data-request.entity';

export interface DataExportJobData {
  requestId: string;
  userId: string;
  exportFormat: 'json' | 'csv' | 'xml' | 'pdf';
  dataTypes: string[];
  includeMetadata?: boolean;
  dateRange?: { start: Date; end: Date };
}

export interface DataDeletionJobData {
  requestId: string;
  userId: string;
  dataTypes?: string[];
  hardDelete?: boolean;
  reason?: string;
}

export interface DataAnonymizationJobData {
  entityType: string;
  entityId: string;
  config: any;
}

@Processor('gdpr-processing')
export class GdprProcessor {
  private readonly logger = new Logger(GdprProcessor.name);

  constructor(
    private readonly gdprService: GdprService,
    private readonly dataPortabilityService: DataPortabilityService,
    private readonly dataAnonymizationService: DataAnonymizationService,
  ) {}

  @Process('process-data-export')
  async processDataExport(job: Job<DataExportJobData>): Promise<void> {
    this.logger.log(`Processing data export job ${job.id} for user ${job.data.userId}`);

    try {
      const { requestId, userId, exportFormat, dataTypes, includeMetadata, dateRange } = job.data;

      // Update request status to processing
      await this.gdprService.updateDataRequestStatus(requestId, DataRequestStatus.PROCESSING);

      // Export the data
      const exportResult = await this.dataPortabilityService.exportUserData(userId, {
        format: exportFormat,
        dataTypes,
        includeMetadata,
        dateRange,
      });

      // Update request with results
      await this.gdprService.updateDataRequestStatus(requestId, DataRequestStatus.COMPLETED, undefined, exportResult);

      this.logger.log(`Data export job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(`Failed to process data export job ${job.id}:`, error);

      // Update request status to failed
      await this.gdprService.updateDataRequestStatus(job.data.requestId, DataRequestStatus.REJECTED, error.message);
      
      throw error;
    }
  }

  @Process('process-data-deletion')
  async processDataDeletion(job: Job<DataDeletionJobData>): Promise<void> {
    this.logger.log(`Processing data deletion job ${job.id} for user ${job.data.userId}`);

    try {
      const { requestId, userId, dataTypes, hardDelete, reason } = job.data;

      // Update request status to processing
      await this.gdprService.updateDataRequestStatus(requestId, DataRequestStatus.PROCESSING);

      // Process data deletion
      const deletionResult = await this.processUserDataDeletion(userId, dataTypes, hardDelete, reason);

      // Update request with results
      await this.gdprService.updateDataRequestStatus(requestId, DataRequestStatus.COMPLETED, undefined, deletionResult);

      this.logger.log(`Data deletion job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(`Failed to process data deletion job ${job.id}:`, error);

      // Update request status to failed
      await this.gdprService.updateDataRequestStatus(job.data.requestId, DataRequestStatus.REJECTED, error.message);
      
      throw error;
    }
  }

  @Process('process-data-anonymization')
  async processDataAnonymization(job: Job<DataAnonymizationJobData>): Promise<void> {
    this.logger.log(`Processing data anonymization job ${job.id} for ${job.data.entityType}:${job.data.entityId}`);

    try {
      const { entityType, entityId, config } = job.data;

      // Get the entity data (this would be implemented based on your data model)
      const entityData = await this.getEntityData(entityType, entityId);

      if (!entityData) {
        throw new Error(`Entity not found: ${entityType}:${entityId}`);
      }

      // Anonymize the data
      const result = await this.dataAnonymizationService.anonymizeData(
        entityType,
        entityId,
        entityData,
        config
      );

      if (result.success) {
        // Update the entity with anonymized data
        await this.updateEntityData(entityType, entityId, result.anonymizedData);
      }

      this.logger.log(`Data anonymization job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(`Failed to process data anonymization job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('process-consent-verification')
  async processConsentVerification(job: Job<{ userId: string; purposes: string[] }>): Promise<void> {
    this.logger.log(`Processing consent verification job ${job.id} for user ${job.data.userId}`);

    try {
      const { userId, purposes } = job.data;

      for (const purpose of purposes) {
        const verification = await this.gdprService.verifyUserConsent(userId, purpose);
        
        if (!verification.hasConsent) {
          this.logger.warn(`Invalid consent found for user ${userId}, purpose ${purpose}`);
          
          // Queue data processing halt or anonymization if needed
          await this.handleInvalidConsent(userId, purpose, verification);
        }
      }

      this.logger.log(`Consent verification job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(`Failed to process consent verification job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('process-data-breach-response')
  async processDataBreachResponse(job: Job<{
    breachId: string;
    affectedUsers: string[];
    dataTypes: string[];
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>): Promise<void> {
    this.logger.log(`Processing data breach response job ${job.id} for breach ${job.data.breachId}`);

    try {
      const { breachId, affectedUsers, dataTypes, severity } = job.data;

      // Log the breach
      await this.gdprService.logDataBreach({
        severity,
        affectedUsers: affectedUsers.length,
        dataTypes,
        description: `Data breach ${breachId} affecting ${affectedUsers.length} users`,
        detectedAt: new Date(),
      });

      // Notify affected users if required
      if (severity === 'high' || severity === 'critical') {
        await this.notifyAffectedUsers(affectedUsers, breachId, dataTypes);
      }

      // Report to authorities if required (GDPR requirement for certain breaches)
      if (severity === 'critical') {
        await this.reportToAuthorities(breachId, affectedUsers.length, dataTypes);
      }

      this.logger.log(`Data breach response job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(`Failed to process data breach response job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('process-compliance-scan')
  async processComplianceScan(job: Job<{ entityType?: string; userId?: string }>): Promise<void> {
    this.logger.log(`Processing compliance scan job ${job.id}`);

    try {
      const { entityType, userId } = job.data;

      const scanResult = await this.gdprService.performComplianceScan({
        includeDataInventory: true,
        includeConsentAudit: true,
        includeSecurityAudit: true,
      });

      // Handle any compliance violations found
      if (scanResult.issues.length > 0) {
        await this.handleComplianceViolations(scanResult.issues);
      }

      this.logger.log(`Compliance scan job ${job.id} completed: ${scanResult.issues.length} violations found`);
    } catch (error) {
      this.logger.error(`Failed to process compliance scan job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('cleanup-expired-data')
  async cleanupExpiredData(job: Job): Promise<void> {
    this.logger.log(`Processing expired data cleanup job ${job.id}`);

    try {
      const cleanupResult = await this.gdprService.cleanupExpiredData();

      this.logger.log(`Expired data cleanup job ${job.id} completed: ${cleanupResult.deletedRecords} records cleaned up`);
    } catch (error) {
      this.logger.error(`Failed to process expired data cleanup job ${job.id}:`, error);
      throw error;
    }
  }

  private async processUserDataDeletion(
    userId: string,
    dataTypes?: string[],
    hardDelete?: boolean,
    reason?: string
  ): Promise<{
    userId: string;
    deletedDataTypes: string[];
    deletionMethod: string;
    timestamp: Date;
    success: boolean;
    details?: any;
  }> {
    const deletedDataTypes: string[] = [];
    const deletionMethod = hardDelete ? 'hard_delete' : 'soft_delete';

    // If no specific data types, delete all user data
    const typesToDelete = dataTypes || ['profile', 'activities', 'preferences', 'communications', 'documents'];

    for (const dataType of typesToDelete) {
      try {
        await this.deleteUserDataType(userId, dataType, hardDelete);
        deletedDataTypes.push(dataType);
      } catch (error) {
        this.logger.error(`Failed to delete ${dataType} for user ${userId}:`, error);
      }
    }

    return {
      userId,
      deletedDataTypes,
      deletionMethod,
      timestamp: new Date(),
      success: deletedDataTypes.length > 0,
      details: {
        reason,
        requestedTypes: typesToDelete,
        successfulDeletions: deletedDataTypes.length,
        failedDeletions: typesToDelete.length - deletedDataTypes.length,
      },
    };
  }

  private async deleteUserDataType(userId: string, dataType: string, hardDelete?: boolean): Promise<void> {
    // This is a placeholder - in a real implementation, you would delete/anonymize
    // data from the appropriate tables based on the dataType
    
    this.logger.log(`${hardDelete ? 'Hard' : 'Soft'} deleting ${dataType} for user ${userId}`);
    
    // Simulate deletion process
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private async getEntityData(entityType: string, entityId: string): Promise<Record<string, any> | null> {
    // Placeholder - would query the appropriate table based on entityType
    return {
      id: entityId,
      name: 'Sample Data',
      email: '<EMAIL>',
      createdAt: new Date(),
    };
  }

  private async updateEntityData(entityType: string, entityId: string, data: Record<string, any>): Promise<void> {
    // Placeholder - would update the appropriate table based on entityType
    this.logger.log(`Updating ${entityType}:${entityId} with anonymized data`);
  }

  private async handleInvalidConsent(userId: string, purpose: string, verification: any): Promise<void> {
    this.logger.warn(`Handling invalid consent for user ${userId}, purpose ${purpose}`);
    
    // This could involve:
    // - Stopping data processing for that purpose
    // - Anonymizing related data
    // - Notifying the user
    // - Creating a compliance violation record
  }

  private async notifyAffectedUsers(userIds: string[], breachId: string, dataTypes: string[]): Promise<void> {
    this.logger.log(`Notifying ${userIds.length} users about data breach ${breachId}`);
    
    // In a real implementation, you would send notifications to affected users
    // This might involve email, in-app notifications, etc.
  }

  private async reportToAuthorities(breachId: string, affectedUserCount: number, dataTypes: string[]): Promise<void> {
    this.logger.log(`Reporting breach ${breachId} to authorities (${affectedUserCount} users affected)`);
    
    // In a real implementation, you would report to relevant data protection authorities
    // This is required under GDPR for certain types of breaches
  }

  private async handleComplianceViolations(violations: any[]): Promise<void> {
    this.logger.warn(`Handling ${violations.length} compliance violations`);
    
    for (const violation of violations) {
      // This could involve:
      // - Creating tickets/alerts
      // - Automatic remediation
      // - Escalation to compliance team
      // - Temporary data processing suspension
    }
  }
}