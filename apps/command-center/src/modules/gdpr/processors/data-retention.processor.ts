import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';

import { DataRetentionService } from '../services/data-retention.service';

export interface DataRetentionJobData {
  entityType: string;
  entityId: string;
  policyId?: string;
  forceExecution?: boolean;
}

export interface BulkRetentionJobData {
  policyId: string;
  entityType: string;
  batchSize?: number;
  dryRun?: boolean;
}

export interface RetentionPolicyEvaluationJobData {
  policyId?: string;
  entityType?: string;
}

@Processor('data-retention')
export class DataRetentionProcessor {
  private readonly logger = new Logger(DataRetentionProcessor.name);

  constructor(private readonly dataRetentionService: DataRetentionService) {}

  @Process('execute-retention')
  async executeRetention(job: Job<DataRetentionJobData>): Promise<void> {
    this.logger.log(`Processing retention execution job ${job.id} for ${job.data.entityType}:${job.data.entityId}`);

    try {
      const { entityType, entityId, forceExecution } = job.data;

      // Check if retention is needed (unless forced)
      if (!forceExecution) {
        const compliance = await this.dataRetentionService.checkRetentionCompliance(entityType, entityId);
        
        if (!compliance.shouldDelete) {
          this.logger.log(`Retention not needed for ${entityType}:${entityId} - ${compliance.reason}`);
          return;
        }
      }

      // Execute retention
      const result = await this.dataRetentionService.executeDataRetention(entityType, entityId);

      if (result.success) {
        this.logger.log(`Retention executed successfully for ${entityType}:${entityId} using ${result.method}`);
      } else {
        this.logger.error(`Retention failed for ${entityType}:${entityId}: ${result.error}`);
      }
    } catch (error) {
      this.logger.error(`Failed to process retention job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('bulk-retention')
  async executeBulkRetention(job: Job<BulkRetentionJobData>): Promise<void> {
    this.logger.log(`Processing bulk retention job ${job.id} for policy ${job.data.policyId}`);

    try {
      const { policyId, entityType, batchSize = 100, dryRun = false } = job.data;

      // Get the retention policy
      const policies = await this.dataRetentionService.getRetentionPolicies(entityType);
      const policy = policies.find(p => p.id === policyId);

      if (!policy) {
        throw new Error(`Retention policy not found: ${policyId}`);
      }

      // Get entities that need retention
      const expiredEntities = await this.findExpiredEntities(policy, batchSize);

      let processedCount = 0;
      let successCount = 0;
      let failureCount = 0;

      for (const entity of expiredEntities) {
        try {
          if (!dryRun) {
            const result = await this.dataRetentionService.executeDataRetention(
              policy.entityType,
              entity.id
            );

            if (result.success) {
              successCount++;
            } else {
              failureCount++;
            }
          }
          
          processedCount++;
        } catch (error) {
          this.logger.error(`Failed to process entity ${entity.id}:`, error);
          failureCount++;
        }
      }

      this.logger.log(`Bulk retention job ${job.id} completed: ${processedCount} processed, ${successCount} successful, ${failureCount} failed`);
    } catch (error) {
      this.logger.error(`Failed to process bulk retention job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('evaluate-retention-policies')
  async evaluateRetentionPolicies(job: Job<RetentionPolicyEvaluationJobData>): Promise<void> {
    this.logger.log(`Processing retention policy evaluation job ${job.id}`);

    try {
      const { policyId, entityType } = job.data;

      let policies = await this.dataRetentionService.getRetentionPolicies(entityType);

      // Filter to specific policy if provided
      if (policyId) {
        policies = policies.filter(p => p.id === policyId);
      }

      for (const policy of policies) {
        await this.evaluatePolicy(policy);
      }

      this.logger.log(`Retention policy evaluation job ${job.id} completed for ${policies.length} policies`);
    } catch (error) {
      this.logger.error(`Failed to process retention policy evaluation job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('retention-compliance-scan')
  async performRetentionComplianceScan(job: Job<{ entityType?: string; sampleSize?: number }>): Promise<void> {
    this.logger.log(`Processing retention compliance scan job ${job.id}`);

    try {
      const { entityType, sampleSize = 1000 } = job.data;

      const scanResult = await this.performComplianceScan(entityType, sampleSize);

      this.logger.log(`Retention compliance scan job ${job.id} completed: ${scanResult.totalScanned} entities scanned, ${scanResult.violations} violations found`);

      // If violations found, queue remediation
      if (scanResult.violations > 0) {
        await this.queueRemediationJobs(scanResult.violationDetails);
      }
    } catch (error) {
      this.logger.error(`Failed to process retention compliance scan job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('retention-report-generation')
  async generateRetentionReport(job: Job<{ reportType: string; period?: { start: Date; end: Date } }>): Promise<void> {
    this.logger.log(`Processing retention report generation job ${job.id}`);

    try {
      const { reportType, period } = job.data;

      const report = await this.createRetentionReport(reportType, period);

      this.logger.log(`Retention report generation job ${job.id} completed: ${report.totalRecords} records in report`);
    } catch (error) {
      this.logger.error(`Failed to process retention report generation job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('cleanup-retention-logs')
  async cleanupRetentionLogs(job: Job<{ retentionDays: number }>): Promise<void> {
    this.logger.log(`Processing retention logs cleanup job ${job.id}`);

    try {
      const { retentionDays } = job.data;

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // Clean up old retention logs
      const deletedCount = await this.cleanupOldRetentionLogs(cutoffDate);

      this.logger.log(`Retention logs cleanup job ${job.id} completed: ${deletedCount} old logs deleted`);
    } catch (error) {
      this.logger.error(`Failed to process retention logs cleanup job ${job.id}:`, error);
      throw error;
    }
  }

  private async findExpiredEntities(policy: any, limit: number): Promise<Array<{ id: string; createdAt: Date }>> {
    // This is a placeholder - in a real implementation, you would query the database
    // to find entities of the specified type that exceed the retention period
    
    const mockEntities: Array<{ id: string; createdAt: Date }> = [];
    
    for (let i = 0; i < Math.min(limit, 50); i++) {
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - policy.retentionPeriodDays - Math.floor(Math.random() * 100));
      
      mockEntities.push({
        id: `entity_${i}_${Date.now()}`,
        createdAt,
      });
    }

    return mockEntities;
  }

  private async evaluatePolicy(policy: any): Promise<void> {
    this.logger.log(`Evaluating retention policy: ${policy.id} for ${policy.entityType}`);

    // Check policy effectiveness and identify potential issues
    const evaluation = {
      policyId: policy.id,
      entityType: policy.entityType,
      retentionPeriod: policy.retentionPeriodDays,
      evaluationDate: new Date(),
      findings: [],
      recommendations: [],
    };

    // Get sample of entities to evaluate
    const sampleEntities = await this.findExpiredEntities(policy, 100);
    
    if (sampleEntities.length > 50) {
      evaluation.findings.push('High number of entities exceeding retention period');
      evaluation.recommendations.push('Consider automated retention processing');
    }

    // Check for policy conflicts
    const conflictingPolicies = await this.findConflictingPolicies(policy);
    if (conflictingPolicies.length > 0) {
      evaluation.findings.push(`Policy conflicts detected with ${conflictingPolicies.length} other policies`);
      evaluation.recommendations.push('Review and resolve policy conflicts');
    }

    this.logger.log(`Policy evaluation completed for ${policy.id}: ${evaluation.findings.length} findings, ${evaluation.recommendations.length} recommendations`);
  }

  private async findConflictingPolicies(policy: any): Promise<any[]> {
    // Check for policies with conflicting rules for the same entity type
    const allPolicies = await this.dataRetentionService.getRetentionPolicies();
    
    return allPolicies.filter(p => 
      p.id !== policy.id && 
      p.entityType === policy.entityType &&
      Math.abs(p.retentionPeriodDays - policy.retentionPeriodDays) > 0
    );
  }

  private async performComplianceScan(entityType?: string, sampleSize = 1000): Promise<{
    totalScanned: number;
    violations: number;
    violationDetails: Array<{ entityType: string; entityId: string; violation: string }>;
  }> {
    const violationDetails: Array<{ entityType: string; entityId: string; violation: string }> = [];
    let totalScanned = 0;

    // Get relevant policies
    const policies = await this.dataRetentionService.getRetentionPolicies(entityType);

    for (const policy of policies) {
      // Sample entities for this policy
      const entities = await this.findExpiredEntities(policy, sampleSize);
      
      for (const entity of entities) {
        totalScanned++;
        
        const compliance = await this.dataRetentionService.checkRetentionCompliance(
          policy.entityType,
          entity.id
        );

        if (!compliance.isCompliant) {
          violationDetails.push({
            entityType: policy.entityType,
            entityId: entity.id,
            violation: compliance.reason || 'Retention period exceeded',
          });
        }
      }
    }

    return {
      totalScanned,
      violations: violationDetails.length,
      violationDetails,
    };
  }

  private async queueRemediationJobs(violations: Array<{ entityType: string; entityId: string; violation: string }>): Promise<void> {
    this.logger.log(`Queueing ${violations.length} remediation jobs`);

    // Group violations by entity type for batch processing
    const violationsByType = violations.reduce((acc, violation) => {
      if (!acc[violation.entityType]) {
        acc[violation.entityType] = [];
      }
      acc[violation.entityType].push(violation);
      return acc;
    }, {} as Record<string, Array<{ entityType: string; entityId: string; violation: string }>>);

    // Queue batch retention jobs for each entity type
    for (const [entityType, entityViolations] of Object.entries(violationsByType)) {
      // This would queue jobs using the job queue service
      this.logger.log(`Would queue bulk retention job for ${entityType} with ${entityViolations.length} entities`);
    }
  }

  private async createRetentionReport(reportType: string, period?: { start: Date; end: Date }): Promise<{
    reportType: string;
    generatedAt: Date;
    period?: { start: Date; end: Date };
    totalRecords: number;
    summary: any;
  }> {
    const generatedAt = new Date();
    const defaultPeriod = period || {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      end: generatedAt,
    };

    // Get retention statistics
    const statistics = await this.dataRetentionService.getRetentionStatistics();

    const report = {
      reportType,
      generatedAt,
      period: defaultPeriod,
      totalRecords: statistics.totalPolicies,
      summary: {
        activePolicies: statistics.activePolicies,
        entitiesProcessedToday: statistics.entitiesProcessedToday,
        entitiesPendingDeletion: statistics.entitiesPendingDeletion,
        retentionByMethod: statistics.retentionByMethod,
      },
    };

    this.logger.log(`Generated ${reportType} retention report with ${report.totalRecords} records`);
    
    return report;
  }

  private async cleanupOldRetentionLogs(cutoffDate: Date): Promise<number> {
    // This would delete old retention processing logs from the database
    // For now, return a mock count
    const deletedCount = Math.floor(Math.random() * 1000);
    
    this.logger.log(`Cleaned up ${deletedCount} retention logs older than ${cutoffDate.toISOString()}`);
    
    return deletedCount;
  }
}