import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Alert } from './entities/alert.entity';
import { NotificationChannel } from './entities/notification-channel.entity';
import { AlertRule } from './entities/alert-rule.entity';

interface AlertData {
  id: string;
  type: string;
  severity: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  metadata?: Record<string, any>;
  timestamp: Date;
  source?: string;
  tags?: string[];
}

interface NotificationConfig {
  email?: {
    enabled: boolean;
    recipients: string[];
    templates: Record<string, string>;
  };
  slack?: {
    enabled: boolean;
    webhook: string;
    channel: string;
    username: string;
  };
  webhook?: {
    enabled: boolean;
    url: string;
    headers?: Record<string, string>;
  };
  sms?: {
    enabled: boolean;
    provider: string;
    apiKey: string;
    recipients: string[];
  };
}

@Injectable()
export class AlertingService {
  private readonly logger = new Logger(AlertingService.name);
  private notificationConfig: NotificationConfig;
  private alertRules: Map<string, AlertRule> = new Map();

  constructor(
    @InjectRepository(Alert)
    private readonly alertRepository: Repository<Alert>,
    @InjectRepository(NotificationChannel)
    private readonly notificationChannelRepository: Repository<NotificationChannel>,
    @InjectRepository(AlertRule)
    private readonly alertRuleRepository: Repository<AlertRule>,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeNotificationConfig();
    this.loadAlertRules();
  }

  private initializeNotificationConfig() {
    this.notificationConfig = {
      email: {
        enabled: this.configService.get<boolean>('ALERTS_EMAIL_ENABLED', false),
        recipients: this.configService.get<string>('ALERTS_EMAIL_RECIPIENTS', '').split(',').filter(Boolean),
        templates: {
          critical: this.configService.get<string>('ALERTS_EMAIL_CRITICAL_TEMPLATE', ''),
          warning: this.configService.get<string>('ALERTS_EMAIL_WARNING_TEMPLATE', ''),
          info: this.configService.get<string>('ALERTS_EMAIL_INFO_TEMPLATE', ''),
        },
      },
      slack: {
        enabled: this.configService.get<boolean>('ALERTS_SLACK_ENABLED', false),
        webhook: this.configService.get<string>('ALERTS_SLACK_WEBHOOK', ''),
        channel: this.configService.get<string>('ALERTS_SLACK_CHANNEL', '#alerts'),
        username: this.configService.get<string>('ALERTS_SLACK_USERNAME', 'Luminar Alerts'),
      },
      webhook: {
        enabled: this.configService.get<boolean>('ALERTS_WEBHOOK_ENABLED', false),
        url: this.configService.get<string>('ALERTS_WEBHOOK_URL', ''),
        headers: {},
      },
      sms: {
        enabled: this.configService.get<boolean>('ALERTS_SMS_ENABLED', false),
        provider: this.configService.get<string>('ALERTS_SMS_PROVIDER', 'twilio'),
        apiKey: this.configService.get<string>('ALERTS_SMS_API_KEY', ''),
        recipients: this.configService.get<string>('ALERTS_SMS_RECIPIENTS', '').split(',').filter(Boolean),
      },
    };
  }

  private async loadAlertRules() {
    const rules = await this.alertRuleRepository.find({
      where: { enabled: true },
    });

    this.alertRules.clear();
    rules.forEach(rule => {
      this.alertRules.set(rule.id, rule);
    });

    this.logger.log(`Loaded ${rules.length} alert rules`);
  }

  @OnEvent('alert.triggered')
  async handleAlertTriggered(alertData: AlertData) {
    try {
      // Check if alert should be suppressed
      if (await this.shouldSuppressAlert(alertData)) {
        return;
      }

      // Check for existing similar alerts
      const existingAlert = await this.findSimilarAlert(alertData);
      if (existingAlert) {
        await this.updateExistingAlert(existingAlert, alertData);
        return;
      }

      // Create new alert
      const alert = this.alertRepository.create({
        alertId: alertData.id,
        type: alertData.type,
        severity: alertData.severity,
        title: alertData.title,
        message: alertData.message,
        metadata: alertData.metadata,
        timestamp: alertData.timestamp,
        source: alertData.source,
        tags: alertData.tags,
        status: 'active',
        acknowledged: false,
        resolved: false,
      });

      await this.alertRepository.save(alert);

      // Send notifications
      await this.sendNotifications(alert);

      // Apply alert rules
      await this.applyAlertRules(alert);

      this.logger.log(`Alert triggered: ${alert.title} (${alert.severity})`);
    } catch (error) {
      this.logger.error('Error handling alert:', error);
    }
  }

  @OnEvent('metrics.collected')
  async handleMetricsCollected(metrics: any) {
    // Check metrics against alert rules
    await this.checkMetricsAgainstRules(metrics);
  }

  @OnEvent('error.detected')
  async handleErrorDetected(error: any) {
    // Create alert for error
    const alertData: AlertData = {
      id: `error_${Date.now()}`,
      type: 'error',
      severity: this.mapErrorSeverity(error.severity),
      title: `Error Detected: ${error.type}`,
      message: error.message,
      metadata: {
        errorId: error.id,
        stackTrace: error.stackTrace,
        context: error.context,
      },
      timestamp: new Date(),
      source: 'error-tracking',
      tags: ['error', error.type],
    };

    await this.handleAlertTriggered(alertData);
  }

  // Public API methods

  public async createAlert(alertData: Omit<AlertData, 'id' | 'timestamp'>): Promise<Alert> {
    const fullAlertData: AlertData = {
      ...alertData,
      id: `manual_${Date.now()}`,
      timestamp: new Date(),
    };

    await this.handleAlertTriggered(fullAlertData);
    return this.alertRepository.findOne({ where: { alertId: fullAlertData.id } });
  }

  public async acknowledgeAlert(alertId: string, acknowledgedBy?: string): Promise<void> {
    const alert = await this.alertRepository.findOne({
      where: { alertId },
    });

    if (!alert) {
      throw new Error('Alert not found');
    }

    alert.acknowledged = true;
    alert.acknowledgedAt = new Date();
    alert.acknowledgedBy = acknowledgedBy;

    await this.alertRepository.save(alert);

    this.eventEmitter.emit('alert.acknowledged', { alert, acknowledgedBy });
  }

  public async resolveAlert(alertId: string, resolvedBy?: string, resolution?: string): Promise<void> {
    const alert = await this.alertRepository.findOne({
      where: { alertId },
    });

    if (!alert) {
      throw new Error('Alert not found');
    }

    alert.resolved = true;
    alert.resolvedAt = new Date();
    alert.resolvedBy = resolvedBy;
    alert.resolution = resolution;
    alert.status = 'resolved';

    await this.alertRepository.save(alert);

    this.eventEmitter.emit('alert.resolved', { alert, resolvedBy, resolution });
  }

  public async getActiveAlerts(filters?: {
    severity?: string;
    service?: string;
    type?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    alerts: Alert[];
    total: number;
  }> {
    const query = this.alertRepository
      .createQueryBuilder('alert')
      .where('alert.status = :status', { status: 'active' })
      .andWhere('alert.resolved = :resolved', { resolved: false });

    if (filters?.severity) {
      query.andWhere('alert.severity = :severity', { severity: filters.severity });
    }

    if (filters?.service) {
      query.andWhere('alert.source = :source', { source: filters.service });
    }

    if (filters?.type) {
      query.andWhere('alert.type = :type', { type: filters.type });
    }

    query.orderBy('alert.timestamp', 'DESC');

    if (filters?.limit) {
      query.limit(filters.limit);
    }

    if (filters?.offset) {
      query.offset(filters.offset);
    }

    const [alerts, total] = await query.getManyAndCount();

    return { alerts, total };
  }

  public async getAlertHistory(filters?: {
    timeRange?: string;
    severity?: string;
    service?: string;
    resolved?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<{
    alerts: Alert[];
    total: number;
  }> {
    const query = this.alertRepository.createQueryBuilder('alert');

    if (filters?.timeRange) {
      const since = this.parseTimeRange(filters.timeRange);
      query.where('alert.timestamp >= :since', { since });
    }

    if (filters?.severity) {
      query.andWhere('alert.severity = :severity', { severity: filters.severity });
    }

    if (filters?.service) {
      query.andWhere('alert.source = :source', { source: filters.service });
    }

    if (filters?.resolved !== undefined) {
      query.andWhere('alert.resolved = :resolved', { resolved: filters.resolved });
    }

    query.orderBy('alert.timestamp', 'DESC');

    if (filters?.limit) {
      query.limit(filters.limit);
    }

    if (filters?.offset) {
      query.offset(filters.offset);
    }

    const [alerts, total] = await query.getManyAndCount();

    return { alerts, total };
  }

  public async createAlertRule(rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<AlertRule> {
    const alertRule = this.alertRuleRepository.create(rule);
    await this.alertRuleRepository.save(alertRule);
    
    this.alertRules.set(alertRule.id, alertRule);
    
    return alertRule;
  }

  public async updateAlertRule(id: string, updates: Partial<AlertRule>): Promise<AlertRule> {
    const rule = await this.alertRuleRepository.findOne({ where: { id } });
    if (!rule) {
      throw new Error('Alert rule not found');
    }

    Object.assign(rule, updates);
    await this.alertRuleRepository.save(rule);
    
    this.alertRules.set(id, rule);
    
    return rule;
  }

  public async deleteAlertRule(id: string): Promise<void> {
    await this.alertRuleRepository.delete(id);
    this.alertRules.delete(id);
  }

  public async testNotificationChannel(channelId: string): Promise<boolean> {
    const channel = await this.notificationChannelRepository.findOne({
      where: { id: channelId },
    });

    if (!channel) {
      throw new Error('Notification channel not found');
    }

    const testAlert = {
      alertId: `test_${Date.now()}`,
      id: `test_${Date.now()}`,
      type: 'test',
      severity: 'info' as const,
      title: 'Test Alert',
      message: 'This is a test alert to verify notification configuration',
      timestamp: new Date(),
      source: 'test',
      status: 'active' as const,
      acknowledged: false,
      resolved: false,
      tags: ['test'],
      metadata: {},
      occurrenceCount: 1,
      priority: 'low' as const,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    try {
      await this.sendNotificationToChannel(channel, testAlert);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send test notification to ${channel.type}:`, error);
      return false;
    }
  }

  // Private helper methods

  private async shouldSuppressAlert(alertData: AlertData): Promise<boolean> {
    // Check for alert suppression rules
    const recentSimilarAlert = await this.alertRepository
      .createQueryBuilder('alert')
      .where('alert.type = :type', { type: alertData.type })
      .andWhere('alert.severity = :severity', { severity: alertData.severity })
      .andWhere('alert.timestamp > :since', { since: new Date(Date.now() - 5 * 60 * 1000) }) // 5 minutes
      .getOne();

    return !!recentSimilarAlert;
  }

  private async findSimilarAlert(alertData: AlertData): Promise<Alert | null> {
    return this.alertRepository
      .createQueryBuilder('alert')
      .where('alert.type = :type', { type: alertData.type })
      .andWhere('alert.source = :source', { source: alertData.source })
      .andWhere('alert.status = :status', { status: 'active' })
      .andWhere('alert.resolved = :resolved', { resolved: false })
      .getOne();
  }

  private async updateExistingAlert(existingAlert: Alert, newAlertData: AlertData): Promise<void> {
    existingAlert.occurrenceCount = (existingAlert.occurrenceCount || 0) + 1;
    existingAlert.lastOccurrence = newAlertData.timestamp;
    existingAlert.message = newAlertData.message;
    existingAlert.metadata = {
      ...existingAlert.metadata,
      ...newAlertData.metadata,
    };

    await this.alertRepository.save(existingAlert);
  }

  private async sendNotifications(alert: Alert): Promise<void> {
    const channels = await this.notificationChannelRepository.find({
      where: { enabled: true },
    });

    const notifications = channels.map(channel => 
      this.sendNotificationToChannel(channel, alert)
    );

    await Promise.allSettled(notifications);
  }

  private async sendNotificationToChannel(channel: NotificationChannel, alert: Alert): Promise<void> {
    try {
      switch (channel.type) {
        case 'email':
          await this.sendEmailNotification(channel, alert);
          break;
        case 'slack':
          await this.sendSlackNotification(channel, alert);
          break;
        case 'webhook':
          await this.sendWebhookNotification(channel, alert);
          break;
        case 'sms':
          await this.sendSmsNotification(channel, alert);
          break;
        default:
          this.logger.warn(`Unknown notification channel type: ${channel.type}`);
      }
    } catch (error) {
      this.logger.error(`Failed to send notification via ${channel.type}:`, error);
    }
  }

  private async sendEmailNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    if (!this.notificationConfig.email?.enabled) return;

    // Implementation would use email service
    // For now, log the notification
    this.logger.log(`Email notification sent: ${alert.title} (${alert.severity})`);
  }

  private async sendSlackNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    if (!this.notificationConfig.slack?.enabled) return;

    const payload = {
      channel: this.notificationConfig.slack.channel,
      username: this.notificationConfig.slack.username,
      text: `🚨 ${alert.title}`,
      attachments: [
        {
          color: this.getSlackColorForSeverity(alert.severity),
          title: alert.title,
          text: alert.message,
          fields: [
            {
              title: 'Severity',
              value: alert.severity.toUpperCase(),
              short: true,
            },
            {
              title: 'Source',
              value: alert.source || 'Unknown',
              short: true,
            },
            {
              title: 'Time',
              value: alert.timestamp.toISOString(),
              short: true,
            },
          ],
        },
      ],
    };

    // Implementation would send to Slack webhook
    this.logger.log(`Slack notification sent: ${alert.title} (${alert.severity})`);
  }

  private async sendWebhookNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    if (!this.notificationConfig.webhook?.enabled) return;

    const payload = {
      alert: {
        id: alert.alertId,
        type: alert.type,
        severity: alert.severity,
        title: alert.title,
        message: alert.message,
        timestamp: alert.timestamp,
        source: alert.source,
        metadata: alert.metadata,
      },
    };

    // Implementation would send HTTP POST to webhook URL
    this.logger.log(`Webhook notification sent: ${alert.title} (${alert.severity})`);
  }

  private async sendSmsNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    if (!this.notificationConfig.sms?.enabled) return;

    const message = `ALERT: ${alert.title} (${alert.severity.toUpperCase()}) - ${alert.message}`;

    // Implementation would use SMS service
    this.logger.log(`SMS notification sent: ${alert.title} (${alert.severity})`);
  }

  private async applyAlertRules(alert: Alert): Promise<void> {
    for (const rule of this.alertRules.values()) {
      if (await this.alertMatchesRule(alert, rule)) {
        await this.executeAlertRule(alert, rule);
      }
    }
  }

  private async alertMatchesRule(alert: Alert, rule: AlertRule): Promise<boolean> {
    // Check if alert matches rule conditions
    if (rule.conditions.severity && !rule.conditions.severity.includes(alert.severity)) {
      return false;
    }

    if (rule.conditions.type && !rule.conditions.type.includes(alert.type)) {
      return false;
    }

    if (rule.conditions.source && !rule.conditions.source.includes(alert.source)) {
      return false;
    }

    if (rule.conditions.tags && rule.conditions.tags.length > 0) {
      const alertTags = alert.tags || [];
      const hasMatchingTag = rule.conditions.tags.some(tag => alertTags.includes(tag));
      if (!hasMatchingTag) {
        return false;
      }
    }

    return true;
  }

  private async executeAlertRule(alert: Alert, rule: AlertRule): Promise<void> {
    for (const action of rule.actions) {
      try {
        await this.executeAlertAction(alert, action);
      } catch (error) {
        this.logger.error(`Failed to execute alert action ${action.type}:`, error);
      }
    }
  }

  private async executeAlertAction(alert: Alert, action: any): Promise<void> {
    switch (action.type) {
      case 'auto_resolve':
        if (action.delay) {
          setTimeout(async () => {
            await this.resolveAlert(alert.alertId, 'auto-resolve', 'Automatically resolved by rule');
          }, action.delay * 1000);
        } else {
          await this.resolveAlert(alert.alertId, 'auto-resolve', 'Automatically resolved by rule');
        }
        break;
      case 'escalate':
        await this.escalateAlert(alert, action.escalationLevel);
        break;
      case 'suppress':
        await this.suppressAlert(alert, action.duration);
        break;
      default:
        this.logger.warn(`Unknown alert action type: ${action.type}`);
    }
  }

  private async escalateAlert(alert: Alert, escalationLevel: string): Promise<void> {
    // Implementation would escalate alert to higher severity or different channels
    this.logger.log(`Alert escalated to ${escalationLevel}: ${alert.title}`);
  }

  private async suppressAlert(alert: Alert, duration: number): Promise<void> {
    // Implementation would suppress similar alerts for specified duration
    this.logger.log(`Alert suppressed for ${duration} seconds: ${alert.title}`);
  }

  private async checkMetricsAgainstRules(metrics: any): Promise<void> {
    // Implementation would check metrics against configured thresholds
    // and trigger alerts if thresholds are exceeded
  }

  private mapErrorSeverity(severity: string): 'critical' | 'warning' | 'info' {
    switch (severity) {
      case 'critical':
      case 'high':
        return 'critical';
      case 'medium':
        return 'warning';
      case 'low':
      default:
        return 'info';
    }
  }

  private getSlackColorForSeverity(severity: string): string {
    switch (severity) {
      case 'critical':
        return 'danger';
      case 'warning':
        return 'warning';
      case 'info':
      default:
        return 'good';
    }
  }

  private parseTimeRange(timeRange: string): Date {
    const now = new Date();
    const match = timeRange.match(/(\d+)([hdwm])/);
    
    if (!match) return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const value = parseInt(match[1]);
    const unit = match[2];
    
    switch (unit) {
      case 'h': return new Date(now.getTime() - value * 60 * 60 * 1000);
      case 'd': return new Date(now.getTime() - value * 24 * 60 * 60 * 1000);
      case 'w': return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000);
      case 'm': return new Date(now.getTime() - value * 30 * 24 * 60 * 60 * 1000);
      default: return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
  }
}