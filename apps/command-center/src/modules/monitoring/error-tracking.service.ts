import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as Sentry from '@sentry/node';
import { ProfilingIntegration } from '@sentry/profiling-node';
import { ErrorEvent } from './entities/error-event.entity';
import { ErrorGroup } from './entities/error-group.entity';
import { ErrorResolution } from './entities/error-resolution.entity';

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  traceId?: string;
  userAgent?: string;
  ip?: string;
  url?: string;
  method?: string;
  statusCode?: number;
  customData?: Record<string, any>;
}

export interface ErrorPattern {
  id: string;
  pattern: RegExp;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  autoResolve: boolean;
  threshold: number;
  timeWindow: number; // in minutes
}

export interface ErrorMetrics {
  totalErrors: number;
  errorRate: number;
  errorsByType: Record<string, number>;
  errorsBySeverity: Record<string, number>;
  errorsByService: Record<string, number>;
  topErrors: Array<{
    id: string;
    message: string;
    count: number;
    lastOccurrence: string;
  }>;
  trends: Array<{
    timestamp: string;
    errorCount: number;
    errorRate: number;
  }>;
}

@Injectable()
export class ErrorTrackingService implements OnModuleInit {
  private readonly logger = new Logger(ErrorTrackingService.name);
  private errorPatterns: ErrorPattern[] = [];
  private errorBuffer: ErrorEvent[] = [];
  private flushInterval: NodeJS.Timeout;

  constructor(
    @InjectRepository(ErrorEvent)
    private readonly errorEventRepository: Repository<ErrorEvent>,
    @InjectRepository(ErrorGroup)
    private readonly errorGroupRepository: Repository<ErrorGroup>,
    @InjectRepository(ErrorResolution)
    private readonly errorResolutionRepository: Repository<ErrorResolution>,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    await this.initializeSentry();
    this.initializeErrorPatterns();
    this.startErrorBufferFlush();
  }

  private async initializeSentry() {
    const dsn = this.configService.get<string>('SENTRY_DSN');
    const environment = this.configService.get<string>('NODE_ENV', 'development');
    const release = this.configService.get<string>('APP_VERSION', '1.0.0');

    if (!dsn) {
      this.logger.warn('Sentry DSN not configured, error tracking will be limited');
      return;
    }

    Sentry.init({
      dsn,
      environment,
      release,
      tracesSampleRate: 1.0,
      profilesSampleRate: 1.0,
      integrations: [
        new ProfilingIntegration(),
        new Sentry.Integrations.Http({ tracing: true }),
        new Sentry.Integrations.Express({ app: undefined }),
        new Sentry.Integrations.Postgres(),
        // Redis integration is not available in current Sentry version
        // new Sentry.Integrations.Redis(),
      ],
      beforeSend: (event, hint) => {
        // Custom filtering logic
        if (this.shouldFilterError(event, hint)) {
          return null;
        }
        return event;
      },
      beforeBreadcrumb: (breadcrumb) => {
        // Filter sensitive data from breadcrumbs
        if (breadcrumb.data) {
          breadcrumb.data = this.sanitizeBreadcrumbData(breadcrumb.data);
        }
        return breadcrumb;
      },
    });

    this.logger.log('Sentry error tracking initialized');
  }

  private initializeErrorPatterns() {
    this.errorPatterns = [
      {
        id: 'database_connection',
        pattern: /connection.*refused|connection.*timeout|connection.*failed/i,
        description: 'Database connection issues',
        severity: 'critical',
        autoResolve: false,
        threshold: 5,
        timeWindow: 5,
      },
      {
        id: 'memory_leak',
        pattern: /out of memory|memory.*exceeded|heap.*limit/i,
        description: 'Memory-related issues',
        severity: 'critical',
        autoResolve: false,
        threshold: 1,
        timeWindow: 1,
      },
      {
        id: 'validation_error',
        pattern: /validation.*failed|invalid.*input|bad.*request/i,
        description: 'Input validation errors',
        severity: 'medium',
        autoResolve: true,
        threshold: 50,
        timeWindow: 15,
      },
      {
        id: 'authentication_error',
        pattern: /unauthorized|authentication.*failed|invalid.*token/i,
        description: 'Authentication/authorization issues',
        severity: 'high',
        autoResolve: false,
        threshold: 10,
        timeWindow: 5,
      },
      {
        id: 'rate_limit',
        pattern: /rate.*limit.*exceeded|too.*many.*requests/i,
        description: 'Rate limiting triggered',
        severity: 'medium',
        autoResolve: true,
        threshold: 100,
        timeWindow: 10,
      },
      {
        id: 'external_service',
        pattern: /external.*service.*unavailable|timeout.*external/i,
        description: 'External service integration issues',
        severity: 'high',
        autoResolve: false,
        threshold: 20,
        timeWindow: 10,
      },
    ];
  }

  private startErrorBufferFlush() {
    this.flushInterval = setInterval(() => {
      this.flushErrorBuffer();
    }, 5000); // Flush every 5 seconds
  }

  private async flushErrorBuffer() {
    if (this.errorBuffer.length === 0) return;

    try {
      const errors = [...this.errorBuffer];
      this.errorBuffer = [];
      
      await this.errorEventRepository.save(errors);
      await this.processErrorGroups(errors);
    } catch (error) {
      this.logger.error('Error flushing error buffer:', error);
      // Add errors back to buffer for retry
      this.errorBuffer.unshift(...this.errorBuffer);
    }
  }

  private async processErrorGroups(errors: ErrorEvent[]) {
    const groupMap = new Map<string, ErrorEvent[]>();
    
    errors.forEach(error => {
      const groupKey = this.generateGroupKey(error);
      if (!groupMap.has(groupKey)) {
        groupMap.set(groupKey, []);
      }
      groupMap.get(groupKey)!.push(error);
    });

    for (const [groupKey, groupErrors] of groupMap) {
      await this.updateErrorGroup(groupKey, groupErrors);
    }
  }

  private generateGroupKey(error: ErrorEvent): string {
    // Group errors by fingerprint (stack trace signature)
    const stackTrace = error.stackTrace || '';
    const message = error.message || '';
    
    // Create a simplified fingerprint
    const fingerprint = `${error.type}_${message.substring(0, 100)}_${this.extractStackSignature(stackTrace)}`;
    return Buffer.from(fingerprint).toString('base64').substring(0, 32);
  }

  private extractStackSignature(stackTrace: string): string {
    const lines = stackTrace.split('\n').slice(0, 3);
    return lines.map(line => line.replace(/:\d+:\d+/g, ':X:X')).join('|');
  }

  private async updateErrorGroup(groupKey: string, errors: ErrorEvent[]) {
    let group = await this.errorGroupRepository.findOne({ where: { fingerprint: groupKey } });
    
    if (!group) {
      const firstError = errors[0];
      group = this.errorGroupRepository.create({
        fingerprint: groupKey,
        type: firstError.type,
        message: firstError.message,
        firstOccurrence: firstError.timestamp,
        lastOccurrence: firstError.timestamp,
        occurrenceCount: 0,
        status: 'active',
        severity: this.calculateSeverity(firstError),
        pattern: this.matchErrorPattern(firstError)?.id,
      });
    }

    group.occurrenceCount += errors.length;
    group.lastOccurrence = new Date();
    
    // Update severity based on frequency
    if (group.occurrenceCount > 100) {
      group.severity = 'critical';
    } else if (group.occurrenceCount > 50) {
      group.severity = 'high';
    } else if (group.occurrenceCount > 20) {
      group.severity = 'medium';
    }

    await this.errorGroupRepository.save(group);
    
    // Check if this error group should trigger an alert
    await this.checkErrorGroupAlerts(group);
  }

  private calculateSeverity(error: ErrorEvent): 'low' | 'medium' | 'high' | 'critical' {
    const pattern = this.matchErrorPattern(error);
    if (pattern) {
      return pattern.severity;
    }
    
    // Default severity based on error type
    if (error.type === 'TypeError' || error.type === 'ReferenceError') {
      return 'high';
    }
    if (error.type === 'ValidationError') {
      return 'medium';
    }
    if (error.statusCode && error.statusCode >= 500) {
      return 'high';
    }
    
    return 'medium';
  }

  private matchErrorPattern(error: ErrorEvent): ErrorPattern | null {
    const message = error.message || '';
    const stackTrace = error.stackTrace || '';
    const combinedText = `${message} ${stackTrace}`;
    
    return this.errorPatterns.find(pattern => 
      pattern.pattern.test(combinedText)
    ) || null;
  }

  private async checkErrorGroupAlerts(group: ErrorGroup) {
    const pattern = this.errorPatterns.find(p => p.id === group.pattern);
    if (!pattern) return;

    const recentErrors = await this.errorEventRepository
      .createQueryBuilder('error')
      .where('error.groupId = :groupId', { groupId: group.id })
      .andWhere('error.timestamp > :since', { 
        since: new Date(Date.now() - pattern.timeWindow * 60 * 1000) 
      })
      .getCount();

    if (recentErrors >= pattern.threshold) {
      await this.triggerAlert(group, pattern, recentErrors);
    }
  }

  private async triggerAlert(group: ErrorGroup, pattern: ErrorPattern, recentCount: number) {
    const alert = {
      id: `error_${group.id}_${Date.now()}`,
      type: 'error_threshold',
      severity: pattern.severity,
      title: `Error threshold exceeded: ${pattern.description}`,
      message: `Error group "${group.message}" has occurred ${recentCount} times in the last ${pattern.timeWindow} minutes (threshold: ${pattern.threshold})`,
      metadata: {
        groupId: group.id,
        patternId: pattern.id,
        occurrenceCount: recentCount,
        threshold: pattern.threshold,
        timeWindow: pattern.timeWindow,
      },
      timestamp: new Date(),
    };

    this.eventEmitter.emit('alert.triggered', alert);
  }

  private shouldFilterError(event: any, hint: any): boolean {
    // Filter out known issues or spam
    const message = event.message || '';
    
    // Filter out client-side errors that are not actionable
    if (message.includes('Script error') || message.includes('Non-Error promise rejection')) {
      return true;
    }
    
    // Filter out bot/crawler errors
    const userAgent = event.request?.headers?.['user-agent'] || '';
    if (/bot|crawler|spider/i.test(userAgent)) {
      return true;
    }
    
    return false;
  }

  private sanitizeBreadcrumbData(data: any): any {
    const sanitized = { ...data };
    
    // Remove sensitive information
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth'];
    sensitiveKeys.forEach(key => {
      if (sanitized[key]) {
        sanitized[key] = '[REDACTED]';
      }
    });
    
    return sanitized;
  }

  // Public API methods

  public async captureError(error: Error, context?: ErrorContext): Promise<string> {
    const errorEvent = this.errorEventRepository.create({
      type: error.constructor.name,
      message: error.message,
      stackTrace: error.stack,
      timestamp: new Date(),
      level: 'error',
      fingerprint: this.generateErrorFingerprint(error),
      context: context || {},
      userId: context?.userId,
      sessionId: context?.sessionId,
      requestId: context?.requestId,
      traceId: context?.traceId,
      userAgent: context?.userAgent,
      ip: context?.ip,
      url: context?.url,
      method: context?.method,
      statusCode: context?.statusCode,
      customData: context?.customData,
    });

    // Send to Sentry
    Sentry.withScope(scope => {
      if (context?.userId) scope.setUser({ id: context.userId });
      if (context?.sessionId) scope.setTag('session', context.sessionId);
      if (context?.requestId) scope.setTag('request', context.requestId);
      if (context?.traceId) scope.setTag('trace', context.traceId);
      if (context?.customData) scope.setContext('custom', context.customData);
      
      Sentry.captureException(error);
    });

    // Add to buffer for batch processing
    this.errorBuffer.push(errorEvent);

    return errorEvent.id;
  }

  public async captureMessage(message: string, level: 'debug' | 'info' | 'warning' | 'error' = 'info', context?: ErrorContext): Promise<string> {
    const errorEvent = this.errorEventRepository.create({
      type: 'Message',
      message,
      timestamp: new Date(),
      level,
      fingerprint: this.generateMessageFingerprint(message),
      context: context || {},
      userId: context?.userId,
      sessionId: context?.sessionId,
      requestId: context?.requestId,
      traceId: context?.traceId,
      userAgent: context?.userAgent,
      ip: context?.ip,
      url: context?.url,
      method: context?.method,
      statusCode: context?.statusCode,
      customData: context?.customData,
    });

    // Send to Sentry
    Sentry.withScope(scope => {
      if (context?.userId) scope.setUser({ id: context.userId });
      if (context?.sessionId) scope.setTag('session', context.sessionId);
      if (context?.requestId) scope.setTag('request', context.requestId);
      if (context?.traceId) scope.setTag('trace', context.traceId);
      if (context?.customData) scope.setContext('custom', context.customData);
      
      Sentry.captureMessage(message, level as any);
    });

    this.errorBuffer.push(errorEvent);

    return errorEvent.id;
  }

  public async getErrors(filters?: {
    timeRange?: string;
    severity?: string;
    service?: string;
    type?: string;
    resolved?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<{
    errors: ErrorGroup[];
    total: number;
    metrics: ErrorMetrics;
  }> {
    const query = this.errorGroupRepository.createQueryBuilder('group');
    
    if (filters?.timeRange) {
      const since = this.parseTimeRange(filters.timeRange);
      query.andWhere('group.lastOccurrence > :since', { since });
    }
    
    if (filters?.severity) {
      query.andWhere('group.severity = :severity', { severity: filters.severity });
    }
    
    if (filters?.type) {
      query.andWhere('group.type = :type', { type: filters.type });
    }
    
    if (filters?.resolved !== undefined) {
      query.andWhere('group.status = :status', { 
        status: filters.resolved ? 'resolved' : 'active' 
      });
    }
    
    query.orderBy('group.lastOccurrence', 'DESC');
    
    if (filters?.limit) {
      query.limit(filters.limit);
    }
    
    if (filters?.offset) {
      query.offset(filters.offset);
    }

    const [errors, total] = await query.getManyAndCount();
    const metrics = await this.getErrorMetrics(filters);

    return { errors, total, metrics };
  }

  public async getErrorDetails(errorId: string): Promise<ErrorGroup | null> {
    const group = await this.errorGroupRepository.findOne({
      where: { id: errorId },
      relations: ['resolution'],
    });

    if (!group) return null;

    // Get recent error events for this group
    const recentEvents = await this.errorEventRepository
      .createQueryBuilder('event')
      .where('event.groupId = :groupId', { groupId: group.id })
      .orderBy('event.timestamp', 'DESC')
      .limit(10)
      .getMany();

    (group as any).recentEvents = recentEvents;

    return group;
  }

  public async resolveError(errorId: string, resolution?: string): Promise<void> {
    const group = await this.errorGroupRepository.findOne({ where: { id: errorId } });
    if (!group) return;

    group.status = 'resolved';
    group.resolvedAt = new Date();
    
    if (resolution) {
      const resolutionEntity = this.errorResolutionRepository.create({
        groupId: group.id,
        resolution,
        createdAt: new Date(),
      });
      await this.errorResolutionRepository.save(resolutionEntity);
    }

    await this.errorGroupRepository.save(group);
  }

  public async getRecentErrors(limit: number = 10): Promise<ErrorEvent[]> {
    return await this.errorEventRepository
      .createQueryBuilder('event')
      .orderBy('event.timestamp', 'DESC')
      .limit(limit)
      .getMany();
  }

  public async getErrorMetrics(filters?: {
    timeRange?: string;
    service?: string;
  }): Promise<ErrorMetrics> {
    const since = filters?.timeRange ? this.parseTimeRange(filters.timeRange) : new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    const query = this.errorEventRepository.createQueryBuilder('event')
      .where('event.timestamp > :since', { since });
    
    if (filters?.service) {
      query.andWhere('event.service = :service', { service: filters.service });
    }

    const errors = await query.getMany();
    const totalErrors = errors.length;
    const totalRequests = await this.getTotalRequests(since);
    const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;

    const errorsByType = errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const errorsBySeverity = errors.reduce((acc, error) => {
      const severity = this.calculateSeverity(error);
      acc[severity] = (acc[severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const errorsByService = errors.reduce((acc, error) => {
      const service = error.service || 'unknown';
      acc[service] = (acc[service] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topErrorGroups = await this.errorGroupRepository
      .createQueryBuilder('group')
      .where('group.lastOccurrence > :since', { since })
      .orderBy('group.occurrenceCount', 'DESC')
      .limit(10)
      .getMany();

    const topErrors = topErrorGroups.map(group => ({
      id: group.id,
      message: group.message,
      count: group.occurrenceCount,
      lastOccurrence: group.lastOccurrence.toISOString(),
    }));

    const trends = await this.getErrorTrends(since);

    return {
      totalErrors,
      errorRate,
      errorsByType,
      errorsBySeverity,
      errorsByService,
      topErrors,
      trends,
    };
  }

  private generateErrorFingerprint(error: Error): string {
    const stack = error.stack || '';
    const message = error.message || '';
    const type = error.constructor.name;
    
    return Buffer.from(`${type}_${message}_${this.extractStackSignature(stack)}`)
      .toString('base64')
      .substring(0, 32);
  }

  private generateMessageFingerprint(message: string): string {
    return Buffer.from(message).toString('base64').substring(0, 32);
  }

  private parseTimeRange(timeRange: string): Date {
    const now = new Date();
    const match = timeRange.match(/(\d+)([hdwm])/);
    
    if (!match) return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const value = parseInt(match[1]);
    const unit = match[2];
    
    switch (unit) {
      case 'h': return new Date(now.getTime() - value * 60 * 60 * 1000);
      case 'd': return new Date(now.getTime() - value * 24 * 60 * 60 * 1000);
      case 'w': return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000);
      case 'm': return new Date(now.getTime() - value * 30 * 24 * 60 * 60 * 1000);
      default: return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
  }

  private async getTotalRequests(since: Date): Promise<number> {
    // This would integrate with your request tracking system
    // For now, return a placeholder
    return 10000;
  }

  private async getErrorTrends(since: Date): Promise<Array<{ timestamp: string; errorCount: number; errorRate: number }>> {
    // This would calculate error trends over time
    // For now, return a placeholder
    return [];
  }
}