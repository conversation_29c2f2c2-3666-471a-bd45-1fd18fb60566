import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Dashboard } from './entities/dashboard.entity';
import { DashboardWidget } from './entities/dashboard-widget.entity';
import { ApmService } from './apm.service';
import { ErrorTrackingService } from './error-tracking.service';
import { UserAnalyticsService } from './user-analytics.service';
import { FeatureAnalyticsService } from './feature-analytics.service';
import { MetricsService } from './metrics.service';
import { AlertingService } from './alerting.service';
import { BusinessIntelligenceReport } from './types';

export interface DashboardConfig {
  id: string;
  name: string;
  description?: string;
  layout: 'grid' | 'flex' | 'tabs';
  widgets: DashboardWidgetConfig[];
  filters?: {
    timeRange?: string;
    services?: string[];
    environments?: string[];
  };
  refreshInterval?: number;
  isPublic?: boolean;
  tags?: string[];
}

export interface DashboardWidgetConfig {
  id: string;
  type: 'chart' | 'table' | 'metric' | 'status' | 'heatmap' | 'text';
  title: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  dataSource: {
    type: 'apm' | 'errors' | 'analytics' | 'features' | 'health' | 'custom';
    query: string;
    params?: Record<string, any>;
  };
  visualization: {
    chartType?: 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'gauge';
    options?: Record<string, any>;
  };
  thresholds?: Array<{
    value: number;
    color: string;
    label: string;
  }>;
}

// BusinessIntelligenceReport moved to types/index.ts

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);
  private defaultDashboards: DashboardConfig[] = [];

  constructor(
    @InjectRepository(Dashboard)
    private readonly dashboardRepository: Repository<Dashboard>,
    @InjectRepository(DashboardWidget)
    private readonly dashboardWidgetRepository: Repository<DashboardWidget>,
    private readonly apmService: ApmService,
    private readonly errorTrackingService: ErrorTrackingService,
    private readonly userAnalyticsService: UserAnalyticsService,
    private readonly featureAnalyticsService: FeatureAnalyticsService,
    private readonly metricsService: MetricsService,
    private readonly alertingService: AlertingService,
  ) {
    this.initializeDefaultDashboards();
  }

  private initializeDefaultDashboards() {
    this.defaultDashboards = [
      {
        id: 'system-overview',
        name: 'System Overview',
        description: 'High-level system health and performance metrics',
        layout: 'grid',
        widgets: [
          {
            id: 'system-health',
            type: 'status',
            title: 'System Health',
            position: { x: 0, y: 0, width: 6, height: 4 },
            dataSource: {
              type: 'health',
              query: 'system_health',
            },
            visualization: {
              chartType: 'gauge',
              options: {
                min: 0,
                max: 100,
                unit: '%',
              },
            },
            thresholds: [
              { value: 90, color: 'green', label: 'Healthy' },
              { value: 70, color: 'yellow', label: 'Degraded' },
              { value: 0, color: 'red', label: 'Unhealthy' },
            ],
          },
          {
            id: 'response-time',
            type: 'chart',
            title: 'Average Response Time',
            position: { x: 6, y: 0, width: 6, height: 4 },
            dataSource: {
              type: 'apm',
              query: 'response_time',
              params: { timeRange: '1h' },
            },
            visualization: {
              chartType: 'line',
              options: {
                yAxisLabel: 'Response Time (ms)',
                xAxisLabel: 'Time',
              },
            },
          },
          {
            id: 'error-rate',
            type: 'chart',
            title: 'Error Rate',
            position: { x: 0, y: 4, width: 6, height: 4 },
            dataSource: {
              type: 'errors',
              query: 'error_rate',
              params: { timeRange: '1h' },
            },
            visualization: {
              chartType: 'area',
              options: {
                yAxisLabel: 'Error Rate (%)',
                xAxisLabel: 'Time',
              },
            },
          },
          {
            id: 'active-users',
            type: 'metric',
            title: 'Active Users',
            position: { x: 6, y: 4, width: 6, height: 4 },
            dataSource: {
              type: 'analytics',
              query: 'active_users',
              params: { timeRange: '1h' },
            },
            visualization: {
              chartType: 'gauge',
              options: {
                unit: 'users',
              },
            },
          },
        ],
        refreshInterval: 30000,
        isPublic: true,
        tags: ['system', 'overview', 'health'],
      },
      {
        id: 'application-performance',
        name: 'Application Performance',
        description: 'Detailed APM metrics and performance analysis',
        layout: 'grid',
        widgets: [
          {
            id: 'throughput',
            type: 'chart',
            title: 'Request Throughput',
            position: { x: 0, y: 0, width: 4, height: 4 },
            dataSource: {
              type: 'apm',
              query: 'throughput',
              params: { timeRange: '24h' },
            },
            visualization: {
              chartType: 'line',
              options: {
                yAxisLabel: 'Requests/sec',
                xAxisLabel: 'Time',
              },
            },
          },
          {
            id: 'latency-percentiles',
            type: 'chart',
            title: 'Latency Percentiles',
            position: { x: 4, y: 0, width: 4, height: 4 },
            dataSource: {
              type: 'apm',
              query: 'latency_percentiles',
              params: { timeRange: '24h' },
            },
            visualization: {
              chartType: 'line',
              options: {
                yAxisLabel: 'Latency (ms)',
                xAxisLabel: 'Time',
                series: ['p50', 'p95', 'p99'],
              },
            },
          },
          {
            id: 'service-map',
            type: 'chart',
            title: 'Service Dependencies',
            position: { x: 8, y: 0, width: 4, height: 4 },
            dataSource: {
              type: 'apm',
              query: 'service_map',
            },
            visualization: {
              chartType: 'scatter',
              options: {
                showConnections: true,
                nodeSize: 'throughput',
                nodeColor: 'health',
              },
            },
          },
          {
            id: 'cpu-usage',
            type: 'chart',
            title: 'CPU Usage',
            position: { x: 0, y: 4, width: 6, height: 4 },
            dataSource: {
              type: 'apm',
              query: 'cpu_usage',
              params: { timeRange: '24h' },
            },
            visualization: {
              chartType: 'area',
              options: {
                yAxisLabel: 'CPU Usage (%)',
                xAxisLabel: 'Time',
              },
            },
          },
          {
            id: 'memory-usage',
            type: 'chart',
            title: 'Memory Usage',
            position: { x: 6, y: 4, width: 6, height: 4 },
            dataSource: {
              type: 'apm',
              query: 'memory_usage',
              params: { timeRange: '24h' },
            },
            visualization: {
              chartType: 'area',
              options: {
                yAxisLabel: 'Memory Usage (MB)',
                xAxisLabel: 'Time',
              },
            },
          },
        ],
        refreshInterval: 60000,
        isPublic: false,
        tags: ['performance', 'apm', 'monitoring'],
      },
      {
        id: 'user-analytics',
        name: 'User Analytics',
        description: 'User behavior and engagement metrics',
        layout: 'grid',
        widgets: [
          {
            id: 'dau-wau-mau',
            type: 'chart',
            title: 'Active Users (DAU/WAU/MAU)',
            position: { x: 0, y: 0, width: 6, height: 4 },
            dataSource: {
              type: 'analytics',
              query: 'active_users_trend',
              params: { timeRange: '30d' },
            },
            visualization: {
              chartType: 'line',
              options: {
                yAxisLabel: 'Active Users',
                xAxisLabel: 'Date',
                series: ['DAU', 'WAU', 'MAU'],
              },
            },
          },
          {
            id: 'user-retention',
            type: 'chart',
            title: 'User Retention',
            position: { x: 6, y: 0, width: 6, height: 4 },
            dataSource: {
              type: 'analytics',
              query: 'user_retention',
              params: { timeRange: '30d' },
            },
            visualization: {
              chartType: 'line',
              options: {
                yAxisLabel: 'Retention Rate (%)',
                xAxisLabel: 'Days',
              },
            },
          },
          {
            id: 'session-metrics',
            type: 'table',
            title: 'Session Metrics',
            position: { x: 0, y: 4, width: 6, height: 4 },
            dataSource: {
              type: 'analytics',
              query: 'session_metrics',
              params: { timeRange: '7d' },
            },
            visualization: {
              options: {
                columns: ['Metric', 'Value', 'Change'],
                sortable: true,
              },
            },
          },
          {
            id: 'user-geography',
            type: 'chart',
            title: 'User Geography',
            position: { x: 6, y: 4, width: 6, height: 4 },
            dataSource: {
              type: 'analytics',
              query: 'user_geography',
              params: { timeRange: '30d' },
            },
            visualization: {
              chartType: 'pie',
              options: {
                showPercentages: true,
                limit: 10,
              },
            },
          },
        ],
        refreshInterval: 300000,
        isPublic: false,
        tags: ['users', 'analytics', 'behavior'],
      },
      {
        id: 'business-intelligence',
        name: 'Business Intelligence',
        description: 'Business metrics and KPIs',
        layout: 'grid',
        widgets: [
          {
            id: 'feature-adoption',
            type: 'chart',
            title: 'Feature Adoption Rate',
            position: { x: 0, y: 0, width: 8, height: 4 },
            dataSource: {
              type: 'features',
              query: 'adoption_rate',
              params: { timeRange: '30d' },
            },
            visualization: {
              chartType: 'bar',
              options: {
                yAxisLabel: 'Adoption Rate (%)',
                xAxisLabel: 'Features',
                horizontal: true,
              },
            },
          },
          {
            id: 'feature-usage-trends',
            type: 'chart',
            title: 'Feature Usage Trends',
            position: { x: 8, y: 0, width: 4, height: 4 },
            dataSource: {
              type: 'features',
              query: 'usage_trends',
              params: { timeRange: '30d' },
            },
            visualization: {
              chartType: 'line',
              options: {
                yAxisLabel: 'Usage Count',
                xAxisLabel: 'Date',
                stacked: true,
              },
            },
          },
          {
            id: 'conversion-funnel',
            type: 'chart',
            title: 'Conversion Funnel',
            position: { x: 0, y: 4, width: 6, height: 4 },
            dataSource: {
              type: 'analytics',
              query: 'conversion_funnel',
              params: { timeRange: '30d' },
            },
            visualization: {
              chartType: 'bar',
              options: {
                yAxisLabel: 'Users',
                xAxisLabel: 'Funnel Steps',
                showConversionRate: true,
              },
            },
          },
          {
            id: 'business-kpis',
            type: 'table',
            title: 'Key Performance Indicators',
            position: { x: 6, y: 4, width: 6, height: 4 },
            dataSource: {
              type: 'custom',
              query: 'business_kpis',
              params: { timeRange: '30d' },
            },
            visualization: {
              options: {
                columns: ['KPI', 'Current', 'Target', 'Change'],
                sortable: true,
                highlight: 'change',
              },
            },
          },
        ],
        refreshInterval: 600000,
        isPublic: false,
        tags: ['business', 'kpis', 'intelligence'],
      },
    ];
  }

  // Public API methods

  public async getDashboards(): Promise<Dashboard[]> {
    const customDashboards = await this.dashboardRepository.find({
      relations: ['widgets'],
      order: { createdAt: 'DESC' },
    });

    const defaultDashboards = this.defaultDashboards.map(config => {
      const dashboard = new Dashboard();
      dashboard.id = config.id;
      dashboard.name = config.name;
      dashboard.description = config.description;
      dashboard.layout = config.layout;
      dashboard.filters = config.filters;
      dashboard.refreshInterval = config.refreshInterval;
      dashboard.isPublic = config.isPublic;
      dashboard.tags = config.tags;
      dashboard.isDefault = true;
      return dashboard;
    });

    return [...defaultDashboards, ...customDashboards];
  }

  public async getDashboard(dashboardId: string): Promise<DashboardConfig | null> {
    // Check if it's a default dashboard
    const defaultDashboard = this.defaultDashboards.find(d => d.id === dashboardId);
    if (defaultDashboard) {
      return defaultDashboard;
    }

    // Check custom dashboards
    const customDashboard = await this.dashboardRepository.findOne({
      where: { id: dashboardId },
      relations: ['widgets'],
    });

    if (!customDashboard) {
      return null;
    }

    return this.convertDashboardToConfig(customDashboard);
  }

  public async createDashboard(config: Omit<DashboardConfig, 'id'>): Promise<Dashboard> {
    const dashboard = this.dashboardRepository.create({
      name: config.name,
      description: config.description,
      layout: config.layout,
      filters: config.filters,
      refreshInterval: config.refreshInterval,
      isPublic: config.isPublic,
      tags: config.tags,
    });

    const savedDashboard = await this.dashboardRepository.save(dashboard);

    // Create widgets
    if (config.widgets && config.widgets.length > 0) {
      const widgets = config.widgets.map(widget => 
        this.dashboardWidgetRepository.create({
          dashboardId: savedDashboard.id,
          widgetId: widget.id,
          type: widget.type,
          title: widget.title,
          position: widget.position,
          dataSource: widget.dataSource,
          visualization: widget.visualization,
          thresholds: widget.thresholds,
        })
      );

      await this.dashboardWidgetRepository.save(widgets);
    }

    return savedDashboard;
  }

  public async updateDashboard(dashboardId: string, updates: Partial<DashboardConfig>): Promise<Dashboard> {
    const dashboard = await this.dashboardRepository.findOne({
      where: { id: dashboardId },
      relations: ['widgets'],
    });

    if (!dashboard) {
      throw new Error('Dashboard not found');
    }

    // Update dashboard properties
    Object.assign(dashboard, updates);
    const savedDashboard = await this.dashboardRepository.save(dashboard);

    // Update widgets if provided
    if (updates.widgets) {
      // Remove existing widgets
      await this.dashboardWidgetRepository.delete({ dashboardId });

      // Create new widgets
      const widgets = updates.widgets.map(widget => 
        this.dashboardWidgetRepository.create({
          dashboardId: savedDashboard.id,
          widgetId: widget.id,
          type: widget.type,
          title: widget.title,
          position: widget.position,
          dataSource: widget.dataSource,
          visualization: widget.visualization,
          thresholds: widget.thresholds,
        })
      );

      await this.dashboardWidgetRepository.save(widgets);
    }

    return savedDashboard;
  }

  public async deleteDashboard(dashboardId: string): Promise<void> {
    const dashboard = await this.dashboardRepository.findOne({
      where: { id: dashboardId },
    });

    if (!dashboard) {
      throw new Error('Dashboard not found');
    }

    // Delete widgets first
    await this.dashboardWidgetRepository.delete({ dashboardId });

    // Delete dashboard
    await this.dashboardRepository.delete(dashboardId);
  }

  public async getDashboardData(dashboardId: string, filters?: {
    timeRange?: string;
    services?: string[];
    environments?: string[];
  }): Promise<Record<string, any>> {
    const dashboard = await this.getDashboard(dashboardId);
    if (!dashboard) {
      throw new Error('Dashboard not found');
    }

    const data: Record<string, any> = {};

    // Fetch data for each widget
    for (const widget of dashboard.widgets) {
      try {
        const widgetData = await this.getWidgetData(widget, filters);
        data[widget.id] = widgetData;
      } catch (error) {
        this.logger.error(`Error fetching data for widget ${widget.id}:`, error);
        data[widget.id] = { error: error.message };
      }
    }

    return data;
  }

  public async generateBusinessIntelligenceReport(options?: {
    reportType?: string;
    timeRange?: string;
    includeRecommendations?: boolean;
  }): Promise<BusinessIntelligenceReport> {
    const reportType = options?.reportType || 'comprehensive';
    const timeRange = options?.timeRange || '30d';
    const includeRecommendations = options?.includeRecommendations !== false;

    const report: BusinessIntelligenceReport = {
      reportId: `bi-${Date.now()}`,
      name: `Business Intelligence Report - ${reportType}`,
      type: 'operational',
      generatedAt: new Date(),
      timeRange: {
        start: new Date(Date.now() - (parseInt(timeRange.replace('d', '')) * 24 * 60 * 60 * 1000)),
        end: new Date()
      },
      metrics: {
        kpis: [],
        insights: [],
        comparisons: [],
      },
      visualizations: [],
      summary: 'Business intelligence report generated successfully',
      recommendations: [],
    };

    // Executive Summary
    const executiveMetrics = await this.getExecutiveSummaryMetrics(timeRange);
    report.metrics.kpis = [
      {
        name: 'System Health',
        value: executiveMetrics.systemHealth,
        target: 95,
        trend: 'stable',
        percentChange: 0,
      },
      {
        name: 'Error Rate',
        value: executiveMetrics.errorRate,
        target: 1,
        trend: 'down',
        percentChange: -5,
      },
      {
        name: 'Active Users',
        value: executiveMetrics.activeUsers,
        target: 1000,
        trend: 'up',
        percentChange: 12,
      },
    ];

    // Recommendations
    if (includeRecommendations) {
      const insights = await this.generateBusinessInsights(timeRange);
      report.metrics.insights = insights.map(insight => ({
        type: 'opportunity',
        title: insight.title,
        description: insight.description,
        impact: insight.impact,
        recommendations: [insight.recommendation],
      }));
    }

    return report;
  }

  // Private helper methods

  private async getWidgetData(widget: DashboardWidgetConfig, filters?: {
    timeRange?: string;
    services?: string[];
    environments?: string[];
  }): Promise<any> {
    const params = {
      ...widget.dataSource.params,
      ...filters,
    };

    switch (widget.dataSource.type) {
      case 'apm':
        return await this.getApmData(widget.dataSource.query, params);
      case 'errors':
        return await this.getErrorData(widget.dataSource.query, params);
      case 'analytics':
        return await this.getAnalyticsData(widget.dataSource.query, params);
      case 'features':
        return await this.getFeatureData(widget.dataSource.query, params);
      case 'health':
        return await this.getHealthData(widget.dataSource.query, params);
      case 'custom':
        return await this.getCustomData(widget.dataSource.query, params);
      default:
        throw new Error(`Unknown data source type: ${widget.dataSource.type}`);
    }
  }

  private async getApmData(query: string, params: any): Promise<any> {
    switch (query) {
      case 'response_time':
        return this.apmService.getLatestMetrics()?.application;
      case 'throughput':
        return this.apmService.getPerformanceMetrics().slice(-24);
      case 'latency_percentiles':
        return this.apmService.getPerformanceMetrics().slice(-24);
      case 'cpu_usage':
        return this.apmService.getPerformanceMetrics().slice(-24).map(m => ({
          timestamp: m.timestamp,
          value: m.cpu.usage,
        }));
      case 'memory_usage':
        return this.apmService.getPerformanceMetrics().slice(-24).map(m => ({
          timestamp: m.timestamp,
          value: m.memory.used,
        }));
      case 'service_map':
        return []; // Would return service dependency graph
      default:
        return [];
    }
  }

  private async getErrorData(query: string, params: any): Promise<any> {
    switch (query) {
      case 'error_rate':
        const metrics = await this.errorTrackingService.getErrorMetrics(params);
        return metrics.trends;
      case 'error_count':
        return await this.errorTrackingService.getErrors(params);
      default:
        return [];
    }
  }

  private async getAnalyticsData(query: string, params: any): Promise<any> {
    switch (query) {
      case 'active_users':
        const analytics = await this.userAnalyticsService.getAnalytics(params);
        return analytics.activeUsers;
      case 'active_users_trend':
        return await this.userAnalyticsService.getAnalytics(params);
      case 'user_retention':
        return await this.userAnalyticsService.getRetentionMetrics(params);
      case 'session_metrics':
        return await this.userAnalyticsService.getBehaviorPatterns(params);
      case 'user_geography':
        const behaviorData = await this.userAnalyticsService.getAnalytics(params);
        return behaviorData.demographics.countries;
      case 'conversion_funnel':
        return await this.userAnalyticsService.getFunnelAnalysis(['signup', 'activation', 'retention'], params);
      default:
        return [];
    }
  }

  private async getFeatureData(query: string, params: any): Promise<any> {
    switch (query) {
      case 'adoption_rate':
        return await this.featureAnalyticsService.getAdoptionMetrics(params);
      case 'usage_trends':
        return await this.featureAnalyticsService.getFeatureUsage(params);
      case 'feature_performance':
        return await this.featureAnalyticsService.getPerformanceMetrics(params);
      default:
        return [];
    }
  }

  private async getHealthData(query: string, params: any): Promise<any> {
    switch (query) {
      case 'system_health':
        return await this.metricsService.getSystemHealth();
      case 'service_health':
        return await this.metricsService.getServiceHealth();
      case 'dependency_health':
        return await this.metricsService.getDependencyHealth();
      default:
        return [];
    }
  }

  private async getCustomData(query: string, params: any): Promise<any> {
    switch (query) {
      case 'business_kpis':
        return [
          { kpi: 'Monthly Active Users', current: 1250, target: 1500, change: '+12%' },
          { kpi: 'Feature Adoption Rate', current: 67, target: 80, change: '+5%' },
          { kpi: 'System Uptime', current: 99.9, target: 99.95, change: '+0.1%' },
          { kpi: 'Error Rate', current: 0.5, target: 0.1, change: '-0.2%' },
        ];
      default:
        return [];
    }
  }

  private convertDashboardToConfig(dashboard: Dashboard): DashboardConfig {
    return {
      id: dashboard.id,
      name: dashboard.name,
      description: dashboard.description,
      layout: dashboard.layout,
      widgets: dashboard.widgets?.map(widget => ({
        id: widget.widgetId,
        type: widget.type,
        title: widget.title,
        position: widget.position,
        dataSource: widget.dataSource,
        visualization: widget.visualization,
        thresholds: widget.thresholds,
      })) || [],
      filters: dashboard.filters,
      refreshInterval: dashboard.refreshInterval,
      isPublic: dashboard.isPublic,
      tags: dashboard.tags,
    };
  }

  private async getExecutiveSummaryMetrics(timeRange: string): Promise<Record<string, any>> {
    const systemHealth = await this.metricsService.getSystemHealth();
    const errorMetrics = await this.errorTrackingService.getErrorMetrics({ timeRange });
    const userMetrics = await this.userAnalyticsService.getAnalytics({ timeRange });
    const featureMetrics = await this.featureAnalyticsService.getFeatureUsage({ timeRange });

    return {
      systemHealth: systemHealth.overallScore,
      errorRate: errorMetrics.errorRate,
      activeUsers: userMetrics.activeUsers.daily,
      featureAdoption: featureMetrics.summary.averageAdoptionRate,
    };
  }

  private async generateExecutiveSummaryText(timeRange: string): Promise<string> {
    const metrics = await this.getExecutiveSummaryMetrics(timeRange);
    
    return `
      System performance over the past ${timeRange} shows a ${metrics.systemHealth}% health score. 
      The error rate is currently at ${metrics.errorRate}%, with ${metrics.activeUsers} daily active users. 
      Feature adoption rate stands at ${metrics.featureAdoption}%.
    `;
  }

  private async generateAnalysisCharts(timeRange: string): Promise<Array<{
    type: string;
    data: any[];
    config: Record<string, any>;
  }>> {
    return [
      {
        type: 'line',
        data: await this.getApmData('response_time', { timeRange }),
        config: {
          title: 'Response Time Trend',
          xAxis: 'timestamp',
          yAxis: 'responseTime',
        },
      },
      {
        type: 'bar',
        data: await this.getFeatureData('adoption_rate', { timeRange }),
        config: {
          title: 'Feature Adoption Rates',
          xAxis: 'feature',
          yAxis: 'adoptionRate',
        },
      },
    ];
  }

  private async generateAnalysisTables(timeRange: string): Promise<Array<{
    headers: string[];
    rows: any[][];
  }>> {
    return [
      {
        headers: ['Service', 'Health', 'Response Time', 'Error Rate'],
        rows: [
          ['Command Center', 'Healthy', '120ms', '0.1%'],
          ['E-Connect', 'Healthy', '95ms', '0.05%'],
          ['Lighthouse', 'Degraded', '250ms', '0.5%'],
        ],
      },
    ];
  }

  private async generateBusinessInsights(timeRange: string): Promise<Array<{
    type: string;
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    recommendation: string;
  }>> {
    return [
      {
        type: 'performance',
        title: 'Response Time Optimization',
        description: 'Some services are experiencing elevated response times',
        impact: 'medium',
        recommendation: 'Implement caching strategies and optimize database queries',
      },
      {
        type: 'features',
        title: 'Low Feature Adoption',
        description: 'Several features have adoption rates below 50%',
        impact: 'high',
        recommendation: 'Improve user onboarding and feature discoverability',
      },
    ];
  }
}