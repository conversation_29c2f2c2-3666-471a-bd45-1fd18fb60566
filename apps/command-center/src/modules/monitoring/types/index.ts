// APM Types
export interface PerformanceMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    heap: {
      used: number;
      total: number;
    };
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  disk: {
    readBytes: number;
    writeBytes: number;
    readOps: number;
    writeOps: number;
  };
  database: {
    activeConnections: number;
    queryTime: number;
    queryCount: number;
  };
  redis: {
    usedMemory: number;
    connectedClients: number;
    commandsProcessed: number;
  };
  application: {
    activeRequests: number;
    responseTime: number;
    errorRate: number;
    throughput: number;
  };
}

export interface AlertThreshold {
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  value: number;
  window: number; // in seconds
}

// Metrics Types
export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  overallScore: number;
  services: ServiceHealth[];
  dependencies: DependencyHealth[];
  summary: {
    totalServices: number;
    healthyServices: number;
    degradedServices: number;
    unhealthyServices: number;
  };
}

export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  errorRate: number;
  lastCheck: Date;
  endpoint: string;
  version?: string;
  dependencies: string[];
}

export interface DependencyHealth {
  name: string;
  type: 'database' | 'cache' | 'queue' | 'external-api' | 'storage';
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  lastCheck: Date;
  connectionPool?: {
    active: number;
    idle: number;
    max: number;
  };
  metrics?: Record<string, any>;
}

export interface TraceData {
  traceId: string;
  spanId: string;
  operationName: string;
  serviceName: string;
  startTime: Date;
  duration: number;
  tags: Record<string, any>;
  logs: Array<{
    timestamp: Date;
    level: string;
    message: string;
    fields: Record<string, any>;
  }>;
  children: TraceData[];
}

interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  message?: string;
  lastChecked: Date;
}

// Error Tracking Types
export interface ErrorContext {
  timestamp: Date;
  errorId: string;
  message: string;
  stack?: string;
  level: 'error' | 'warning' | 'info';
  userId?: string;
  sessionId?: string;
  requestId?: string;
  userAgent?: string;
  url?: string;
  method?: string;
  statusCode?: number;
  tags?: Record<string, string>;
  extra?: Record<string, any>;
  breadcrumbs?: Array<{
    timestamp: Date;
    type: string;
    category: string;
    message: string;
    data?: Record<string, any>;
  }>;
}

export interface ErrorPattern {
  pattern: string;
  count: number;
  firstSeen: Date;
  lastSeen: Date;
  affectedUsers: number;
  examples: ErrorContext[];
}

export interface ErrorMetrics {
  totalErrors: number;
  errorRate: number;
  errorsByLevel: Record<string, number>;
  errorsByService: Record<string, number>;
  topErrors: ErrorPattern[];
  affectedUsers: number;
  resolvedErrors: number;
  unresolvedErrors: number;
  averageResolutionTime: number;
}

// User Analytics Types
export interface UserAnalyticsEvent {
  eventId: string;
  userId: string;
  sessionId: string;
  eventType: string;
  eventProperties: Record<string, any>;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
  location?: {
    country?: string;
    city?: string;
    region?: string;
  };
  device?: {
    type?: string;
    os?: string;
    browser?: string;
  };
  referrer?: string;
  pageUrl?: string;
}

export interface UserBehaviorMetrics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  returningUsers: number;
  averageSessionDuration: number;
  bounceRate: number;
  pageViewsPerSession: number;
  topPages: Array<{
    page: string;
    views: number;
    uniqueVisitors: number;
    averageTimeOnPage: number;
  }>;
  userFlow: Array<{
    from: string;
    to: string;
    count: number;
  }>;
  userRetention: {
    day1: number;
    day7: number;
    day30: number;
  };
  deviceBreakdown: Record<string, number>;
  locationBreakdown: Record<string, number>;
}

export interface PrivacySettings {
  anonymizeIp: boolean;
  respectDoNotTrack: boolean;
  cookieConsent: boolean;
  dataRetentionDays: number;
  excludedEvents: string[];
  excludedProperties: string[];
}

// Feature Analytics Types
export interface FeatureUsageData {
  featureName: string;
  userId?: string;
  sessionId: string;
  timestamp: Date;
  duration?: number;
  interactionCount?: number;
  completionRate?: number;
  errorCount?: number;
  metadata?: Record<string, any>;
}

export interface FeaturePerformanceMetrics {
  featureName: string;
  totalUsage: number;
  uniqueUsers: number;
  averageDuration: number;
  completionRate: number;
  errorRate: number;
  adoptionRate: number;
  retentionRate: number;
  satisfactionScore?: number;
  performanceTrend: Array<{
    date: Date;
    usage: number;
    performance: number;
  }>;
  userSegments: Array<{
    segment: string;
    usage: number;
    performance: number;
  }>;
}

// Dashboard Types
export interface DashboardConfig {
  id: string;
  name: string;
  description?: string;
  layout: 'grid' | 'flex' | 'tabs';
  widgets: DashboardWidgetConfig[];
  refreshInterval?: number;
  timeRange?: string;
  filters?: {
    timeRange?: string;
    services?: string[];
    environments?: string[];
  };
  isPublic?: boolean;
  tags?: string[];
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface DashboardWidgetConfig {
  id: string;
  type: 'chart' | 'table' | 'metric' | 'status' | 'heatmap' | 'text';
  title: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  dataSource: {
    type: 'apm' | 'errors' | 'analytics' | 'features' | 'health' | 'custom';
    query: string;
    params?: Record<string, any>;
  };
  visualization: {
    chartType?: 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'gauge';
    options?: Record<string, any>;
  };
  thresholds?: Array<{
    value: number;
    color: string;
    label: string;
  }>;
}

// Shared Types
export interface BusinessIntelligenceReport {
  reportId: string;
  name: string;
  type: 'executive' | 'operational' | 'tactical' | 'strategic';
  generatedAt: Date;
  timeRange: {
    start: Date;
    end: Date;
  };
  metrics: {
    kpis: Array<{
      name: string;
      value: number;
      target: number;
      trend: 'up' | 'down' | 'stable';
      percentChange: number;
    }>;
    insights: Array<{
      type: 'opportunity' | 'risk' | 'trend' | 'anomaly';
      title: string;
      description: string;
      impact: 'high' | 'medium' | 'low';
      recommendations: string[];
    }>;
    comparisons: Array<{
      metric: string;
      current: number;
      previous: number;
      change: number;
      percentChange: number;
    }>;
  };
  visualizations: Array<{
    type: string;
    title: string;
    data: any;
  }>;
  summary: string;
  recommendations: string[];
}