import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ApmService } from './apm.service';

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  overallScore: number;
  services: ServiceHealth[];
  dependencies: DependencyHealth[];
  summary: {
    totalServices: number;
    healthyServices: number;
    degradedServices: number;
    unhealthyServices: number;
  };
}

export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  errorRate: number;
  lastCheck: Date;
  endpoint: string;
  version?: string;
  dependencies: string[];
}

export interface DependencyHealth {
  name: string;
  type: 'database' | 'cache' | 'queue' | 'external-api' | 'storage';
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  lastCheck: Date;
  connectionPool?: {
    active: number;
    idle: number;
    max: number;
  };
  metrics?: Record<string, any>;
}

export interface TraceData {
  traceId: string;
  spanId: string;
  operationName: string;
  serviceName: string;
  startTime: Date;
  duration: number;
  tags: Record<string, any>;
  logs: Array<{
    timestamp: Date;
    level: string;
    message: string;
    fields: Record<string, any>;
  }>;
  children: TraceData[];
}

@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);
  private healthCheckInterval: NodeJS.Timeout;
  private serviceEndpoints: Map<string, string> = new Map();
  private dependencyCheckers: Map<string, () => Promise<DependencyHealth>> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly apmService: ApmService,
  ) {
    this.initializeServiceEndpoints();
    this.initializeDependencyCheckers();
    this.startHealthChecks();
  }

  private initializeServiceEndpoints() {
    // Configure service endpoints for health checks
    this.serviceEndpoints.set('command-center', this.configService.get<string>('COMMAND_CENTER_URL', 'http://localhost:3000'));
    this.serviceEndpoints.set('e-connect', this.configService.get<string>('E_CONNECT_URL', 'http://localhost:3001'));
    this.serviceEndpoints.set('lighthouse', this.configService.get<string>('LIGHTHOUSE_URL', 'http://localhost:3002'));
    this.serviceEndpoints.set('training', this.configService.get<string>('TRAINING_URL', 'http://localhost:3003'));
    this.serviceEndpoints.set('vendors', this.configService.get<string>('VENDORS_URL', 'http://localhost:3004'));
    this.serviceEndpoints.set('wins', this.configService.get<string>('WINS_URL', 'http://localhost:3005'));
    this.serviceEndpoints.set('amna', this.configService.get<string>('AMNA_URL', 'http://localhost:3006'));
  }

  private initializeDependencyCheckers() {
    // Initialize dependency health checkers
    this.dependencyCheckers.set('postgres', this.checkPostgresHealth.bind(this));
    this.dependencyCheckers.set('redis', this.checkRedisHealth.bind(this));
    this.dependencyCheckers.set('minio', this.checkMinioHealth.bind(this));
    this.dependencyCheckers.set('qdrant', this.checkQdrantHealth.bind(this));
    this.dependencyCheckers.set('ollama', this.checkOllamaHealth.bind(this));
    this.dependencyCheckers.set('elasticsearch', this.checkElasticsearchHealth.bind(this));
    this.dependencyCheckers.set('rabbitmq', this.checkRabbitMQHealth.bind(this));
  }

  private startHealthChecks() {
    // Run health checks every 30 seconds
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks();
    }, 30000);
  }

  private async performHealthChecks() {
    try {
      await this.getSystemHealth();
    } catch (error) {
      this.logger.error('Error performing health checks:', error);
    }
  }

  // Public API methods

  public async getSystemHealth(): Promise<SystemHealth> {
    const [services, dependencies] = await Promise.all([
      this.checkAllServices(),
      this.checkAllDependencies(),
    ]);

    const totalServices = services.length;
    const healthyServices = services.filter(s => s.status === 'healthy').length;
    const degradedServices = services.filter(s => s.status === 'degraded').length;
    const unhealthyServices = services.filter(s => s.status === 'unhealthy').length;

    const healthyDependencies = dependencies.filter(d => d.status === 'healthy').length;
    const totalDependencies = dependencies.length;

    // Calculate overall health score
    const serviceScore = totalServices > 0 ? (healthyServices / totalServices) * 100 : 100;
    const dependencyScore = totalDependencies > 0 ? (healthyDependencies / totalDependencies) * 100 : 100;
    const overallScore = (serviceScore + dependencyScore) / 2;

    // Determine overall status
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (overallScore >= 90) {
      status = 'healthy';
    } else if (overallScore >= 70) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      timestamp: new Date(),
      overallScore,
      services,
      dependencies,
      summary: {
        totalServices,
        healthyServices,
        degradedServices,
        unhealthyServices,
      },
    };
  }

  public async getServiceHealth(): Promise<ServiceHealth[]> {
    return await this.checkAllServices();
  }

  public async getDependencyHealth(): Promise<DependencyHealth[]> {
    return await this.checkAllDependencies();
  }

  public async getTraces(filters?: {
    traceId?: string;
    service?: string;
    operation?: string;
    timeRange?: string;
    limit?: number;
  }): Promise<TraceData[]> {
    // Implementation would query trace data from OpenTelemetry collector
    // For now, return mock data
    return [
      {
        traceId: '12345',
        spanId: 'span-1',
        operationName: 'api.request',
        serviceName: 'command-center',
        startTime: new Date(),
        duration: 150,
        tags: {
          'http.method': 'GET',
          'http.url': '/api/health',
          'http.status_code': 200,
        },
        logs: [
          {
            timestamp: new Date(),
            level: 'info',
            message: 'Request received',
            fields: {
              'user.id': 'user123',
              'request.id': 'req-456',
            },
          },
        ],
        children: [],
      },
    ];
  }

  // Private helper methods

  private async checkAllServices(): Promise<ServiceHealth[]> {
    const serviceChecks = Array.from(this.serviceEndpoints.entries()).map(
      ([name, endpoint]) => this.checkServiceHealth(name, endpoint)
    );

    const results = await Promise.allSettled(serviceChecks);
    return results.map((result, index) => {
      const [name] = Array.from(this.serviceEndpoints.entries())[index];
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          name,
          status: 'unhealthy' as const,
          responseTime: 0,
          errorRate: 100,
          lastCheck: new Date(),
          endpoint: this.serviceEndpoints.get(name)!,
          dependencies: [],
        };
      }
    });
  }

  private async checkServiceHealth(name: string, endpoint: string): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${endpoint}/health`, {
          timeout: 5000,
        })
      );

      const responseTime = Date.now() - startTime;
      const data = response.data;

      return {
        name,
        status: data.status === 'ok' ? 'healthy' : 'degraded',
        responseTime,
        errorRate: 0,
        lastCheck: new Date(),
        endpoint,
        version: data.version,
        dependencies: data.dependencies || [],
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        name,
        status: 'unhealthy',
        responseTime,
        errorRate: 100,
        lastCheck: new Date(),
        endpoint,
        dependencies: [],
      };
    }
  }

  private async checkAllDependencies(): Promise<DependencyHealth[]> {
    const dependencyChecks = Array.from(this.dependencyCheckers.entries()).map(
      ([name, checker]) => checker()
    );

    const results = await Promise.allSettled(dependencyChecks);
    return results.map((result, index) => {
      const [name] = Array.from(this.dependencyCheckers.entries())[index];
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          name,
          type: 'external-api' as const,
          status: 'unhealthy' as const,
          responseTime: 0,
          lastCheck: new Date(),
        };
      }
    });
  }

  private async checkPostgresHealth(): Promise<DependencyHealth> {
    const startTime = Date.now();
    
    try {
      // Implementation would check PostgreSQL connection
      // For now, return mock data
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'postgres',
        type: 'database',
        status: 'healthy',
        responseTime,
        lastCheck: new Date(),
        connectionPool: {
          active: 5,
          idle: 10,
          max: 20,
        },
        metrics: {
          connectionsTotal: 15,
          queriesPerSecond: 120,
          slowQueries: 2,
        },
      };
    } catch (error) {
      return {
        name: 'postgres',
        type: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
      };
    }
  }

  private async checkRedisHealth(): Promise<DependencyHealth> {
    const startTime = Date.now();
    
    try {
      // Implementation would check Redis connection
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'redis',
        type: 'cache',
        status: 'healthy',
        responseTime,
        lastCheck: new Date(),
        metrics: {
          usedMemory: 50 * 1024 * 1024, // 50MB
          connectedClients: 10,
          commandsProcessed: 1000,
          hitRate: 95.5,
        },
      };
    } catch (error) {
      return {
        name: 'redis',
        type: 'cache',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
      };
    }
  }

  private async checkMinioHealth(): Promise<DependencyHealth> {
    const startTime = Date.now();
    
    try {
      // Implementation would check MinIO connection
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'minio',
        type: 'storage',
        status: 'healthy',
        responseTime,
        lastCheck: new Date(),
        metrics: {
          bucketsCount: 5,
          objectsCount: 1250,
          storageUsed: 2.5 * 1024 * 1024 * 1024, // 2.5GB
          storageAvailable: 100 * 1024 * 1024 * 1024, // 100GB
        },
      };
    } catch (error) {
      return {
        name: 'minio',
        type: 'storage',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
      };
    }
  }

  private async checkQdrantHealth(): Promise<DependencyHealth> {
    const startTime = Date.now();
    
    try {
      // Implementation would check Qdrant connection
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'qdrant',
        type: 'database',
        status: 'healthy',
        responseTime,
        lastCheck: new Date(),
        metrics: {
          collectionsCount: 3,
          vectorsCount: 10000,
          memoryUsage: 512 * 1024 * 1024, // 512MB
          diskUsage: 1024 * 1024 * 1024, // 1GB
        },
      };
    } catch (error) {
      return {
        name: 'qdrant',
        type: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
      };
    }
  }

  private async checkOllamaHealth(): Promise<DependencyHealth> {
    const startTime = Date.now();
    
    try {
      // Implementation would check Ollama connection
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'ollama',
        type: 'external-api',
        status: 'healthy',
        responseTime,
        lastCheck: new Date(),
        metrics: {
          modelsLoaded: 2,
          totalModels: 5,
          gpuMemoryUsed: 4 * 1024 * 1024 * 1024, // 4GB
          gpuMemoryTotal: 8 * 1024 * 1024 * 1024, // 8GB
        },
      };
    } catch (error) {
      return {
        name: 'ollama',
        type: 'external-api',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
      };
    }
  }

  private async checkElasticsearchHealth(): Promise<DependencyHealth> {
    const startTime = Date.now();
    
    try {
      // Implementation would check Elasticsearch connection
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'elasticsearch',
        type: 'database',
        status: 'healthy',
        responseTime,
        lastCheck: new Date(),
        metrics: {
          indicesCount: 10,
          documentsCount: 500000,
          clusterHealth: 'green',
          activeShards: 20,
        },
      };
    } catch (error) {
      return {
        name: 'elasticsearch',
        type: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
      };
    }
  }

  private async checkRabbitMQHealth(): Promise<DependencyHealth> {
    const startTime = Date.now();
    
    try {
      // Implementation would check RabbitMQ connection
      const responseTime = Date.now() - startTime;
      
      return {
        name: 'rabbitmq',
        type: 'queue',
        status: 'healthy',
        responseTime,
        lastCheck: new Date(),
        metrics: {
          queuesCount: 8,
          messagesReady: 50,
          messagesUnacknowledged: 5,
          connectionsCount: 12,
        },
      };
    } catch (error) {
      return {
        name: 'rabbitmq',
        type: 'queue',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
      };
    }
  }
}