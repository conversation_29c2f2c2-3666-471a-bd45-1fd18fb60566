import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { ApmService } from './apm.service';
import { ErrorTrackingService } from './error-tracking.service';
import { UserAnalyticsService } from './user-analytics.service';
import { FeatureAnalyticsService } from './feature-analytics.service';
import { AlertingService } from './alerting.service';
import { MetricsService } from './metrics.service';
import { DashboardService } from './dashboard.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { 
  PerformanceMetrics, 
  TraceData, 
  ErrorMetrics, 
  UserBehaviorMetrics, 
  FeaturePerformanceMetrics, 
  DashboardConfig, 
  SystemHealth, 
  ServiceHealth, 
  DependencyHealth, 
  BusinessIntelligenceReport 
} from './types';

@ApiTags('Monitoring & Analytics')
@Controller('monitoring')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
export class MonitoringController {
  constructor(
    private readonly apmService: ApmService,
    private readonly errorTrackingService: ErrorTrackingService,
    private readonly userAnalyticsService: UserAnalyticsService,
    private readonly featureAnalyticsService: FeatureAnalyticsService,
    private readonly alertingService: AlertingService,
    private readonly metricsService: MetricsService,
    private readonly dashboardService: DashboardService,
  ) {}

  // APM Endpoints
  @Get('apm/metrics')
  @ApiOperation({ summary: 'Get APM performance metrics' })
  @ApiResponse({ status: 200, description: 'Performance metrics retrieved successfully' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range for metrics (e.g., 1h, 24h, 7d)' })
  async getApmMetrics(@Query('timeRange') timeRange?: string): Promise<{ current: PerformanceMetrics; history: PerformanceMetrics[]; timeRange: string }> {
    return {
      current: this.apmService.getLatestMetrics(),
      history: this.apmService.getPerformanceMetrics(),
      timeRange: timeRange || '1h',
    };
  }

  @Get('apm/traces')
  @ApiOperation({ summary: 'Get distributed tracing data' })
  @ApiResponse({ status: 200, description: 'Trace data retrieved successfully' })
  @ApiQuery({ name: 'traceId', required: false, description: 'Specific trace ID to retrieve' })
  @ApiQuery({ name: 'service', required: false, description: 'Filter by service name' })
  @ApiQuery({ name: 'operation', required: false, description: 'Filter by operation name' })
  async getTraces(
    @Query('traceId') traceId?: string,
    @Query('service') service?: string,
    @Query('operation') operation?: string,
  ): Promise<TraceData[]> {
    return await this.metricsService.getTraces({ traceId, service, operation });
  }

  @Put('apm/alerts')
  @ApiOperation({ summary: 'Update APM alert thresholds' })
  @ApiResponse({ status: 200, description: 'Alert thresholds updated successfully' })
  async updateAlertThresholds(@Body() thresholds: any[]) {
    thresholds.forEach(threshold => {
      this.apmService.updateAlertThreshold(
        threshold.metric,
        threshold.value,
        threshold.comparison,
        threshold.severity,
      );
    });
    return { message: 'Alert thresholds updated successfully' };
  }

  // Error Tracking Endpoints
  @Get('errors')
  @ApiOperation({ summary: 'Get error tracking data' })
  @ApiResponse({ status: 200, description: 'Error data retrieved successfully' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range for error data' })
  @ApiQuery({ name: 'severity', required: false, description: 'Filter by error severity' })
  @ApiQuery({ name: 'service', required: false, description: 'Filter by service name' })
  async getErrors(
    @Query('timeRange') timeRange?: string,
    @Query('severity') severity?: string,
    @Query('service') service?: string,
  ) {
    return await this.errorTrackingService.getErrors({ timeRange, severity, service });
  }

  @Get('errors/:errorId')
  @ApiOperation({ summary: 'Get specific error details' })
  @ApiParam({ name: 'errorId', description: 'Error ID' })
  @ApiResponse({ status: 200, description: 'Error details retrieved successfully' })
  async getErrorDetails(@Param('errorId') errorId: string) {
    return await this.errorTrackingService.getErrorDetails(errorId);
  }

  @Post('errors/:errorId/resolve')
  @ApiOperation({ summary: 'Mark error as resolved' })
  @ApiParam({ name: 'errorId', description: 'Error ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async resolveError(@Param('errorId') errorId: string) {
    await this.errorTrackingService.resolveError(errorId);
  }

  // User Analytics Endpoints
  @Get('analytics/users')
  @ApiOperation({ summary: 'Get user analytics data' })
  @ApiResponse({ status: 200, description: 'User analytics retrieved successfully' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range for analytics' })
  @ApiQuery({ name: 'metric', required: false, description: 'Specific metric to retrieve' })
  async getUserAnalytics(
    @Query('timeRange') timeRange?: string,
    @Query('metric') metric?: string,
  ) {
    return await this.userAnalyticsService.getAnalytics({ timeRange, metric });
  }

  @Get('analytics/users/behavior')
  @ApiOperation({ summary: 'Get user behavior patterns' })
  @ApiResponse({ status: 200, description: 'User behavior data retrieved successfully' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range for behavior data' })
  async getUserBehavior(@Query('timeRange') timeRange?: string) {
    return await this.userAnalyticsService.getBehaviorPatterns({ timeRange });
  }

  @Get('analytics/users/retention')
  @ApiOperation({ summary: 'Get user retention metrics' })
  @ApiResponse({ status: 200, description: 'User retention data retrieved successfully' })
  @ApiQuery({ name: 'cohort', required: false, description: 'Cohort period (daily, weekly, monthly)' })
  async getUserRetention(@Query('cohort') cohort?: string) {
    return await this.userAnalyticsService.getRetentionMetrics({ cohort });
  }

  // Feature Analytics Endpoints
  @Get('analytics/features')
  @ApiOperation({ summary: 'Get feature usage analytics' })
  @ApiResponse({ status: 200, description: 'Feature analytics retrieved successfully' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range for analytics' })
  @ApiQuery({ name: 'feature', required: false, description: 'Specific feature to analyze' })
  async getFeatureAnalytics(
    @Query('timeRange') timeRange?: string,
    @Query('feature') feature?: string,
  ): Promise<{ features: FeaturePerformanceMetrics[]; summary: { totalFeatures: number; totalUsage: number; averageAdoptionRate: number } }> {
    return await this.featureAnalyticsService.getFeatureUsage({ timeRange, feature });
  }

  @Get('analytics/features/adoption')
  @ApiOperation({ summary: 'Get feature adoption metrics' })
  @ApiResponse({ status: 200, description: 'Feature adoption data retrieved successfully' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range for adoption data' })
  async getFeatureAdoption(@Query('timeRange') timeRange?: string) {
    return await this.featureAnalyticsService.getAdoptionMetrics({ timeRange });
  }

  @Get('analytics/features/performance')
  @ApiOperation({ summary: 'Get feature performance metrics' })
  @ApiResponse({ status: 200, description: 'Feature performance data retrieved successfully' })
  @ApiQuery({ name: 'feature', required: false, description: 'Specific feature to analyze' })
  async getFeaturePerformance(@Query('feature') feature?: string): Promise<{ performance: FeaturePerformanceMetrics[]; benchmarks: { averageSuccessRate: number; averageDuration: number; averageErrorRate: number } }> {
    return await this.featureAnalyticsService.getPerformanceMetrics({ feature });
  }

  // Alerting Endpoints
  @Get('alerts')
  @ApiOperation({ summary: 'Get active alerts' })
  @ApiResponse({ status: 200, description: 'Active alerts retrieved successfully' })
  @ApiQuery({ name: 'severity', required: false, description: 'Filter by alert severity' })
  @ApiQuery({ name: 'service', required: false, description: 'Filter by service name' })
  async getAlerts(
    @Query('severity') severity?: string,
    @Query('service') service?: string,
  ) {
    return await this.alertingService.getActiveAlerts({ severity, service });
  }

  @Get('alerts/history')
  @ApiOperation({ summary: 'Get alert history' })
  @ApiResponse({ status: 200, description: 'Alert history retrieved successfully' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range for alert history' })
  async getAlertHistory(@Query('timeRange') timeRange?: string) {
    return await this.alertingService.getAlertHistory({ timeRange });
  }

  @Post('alerts/:alertId/acknowledge')
  @ApiOperation({ summary: 'Acknowledge an alert' })
  @ApiParam({ name: 'alertId', description: 'Alert ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async acknowledgeAlert(@Param('alertId') alertId: string) {
    await this.alertingService.acknowledgeAlert(alertId);
  }

  @Post('alerts/:alertId/resolve')
  @ApiOperation({ summary: 'Resolve an alert' })
  @ApiParam({ name: 'alertId', description: 'Alert ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async resolveAlert(@Param('alertId') alertId: string) {
    await this.alertingService.resolveAlert(alertId);
  }

  // Dashboard Endpoints
  @Get('dashboards')
  @ApiOperation({ summary: 'Get available dashboards' })
  @ApiResponse({ status: 200, description: 'Dashboards retrieved successfully' })
  async getDashboards() {
    return await this.dashboardService.getDashboards();
  }

  @Get('dashboards/:dashboardId')
  @ApiOperation({ summary: 'Get specific dashboard configuration' })
  @ApiParam({ name: 'dashboardId', description: 'Dashboard ID' })
  @ApiResponse({ status: 200, description: 'Dashboard configuration retrieved successfully' })
  async getDashboard(@Param('dashboardId') dashboardId: string): Promise<DashboardConfig> {
    return await this.dashboardService.getDashboard(dashboardId);
  }

  @Post('dashboards')
  @ApiOperation({ summary: 'Create custom dashboard' })
  @ApiResponse({ status: 201, description: 'Dashboard created successfully' })
  async createDashboard(@Body() dashboard: any) {
    return await this.dashboardService.createDashboard(dashboard);
  }

  @Put('dashboards/:dashboardId')
  @ApiOperation({ summary: 'Update dashboard configuration' })
  @ApiParam({ name: 'dashboardId', description: 'Dashboard ID' })
  @ApiResponse({ status: 200, description: 'Dashboard updated successfully' })
  async updateDashboard(@Param('dashboardId') dashboardId: string, @Body() dashboard: any) {
    return await this.dashboardService.updateDashboard(dashboardId, dashboard);
  }

  @Delete('dashboards/:dashboardId')
  @ApiOperation({ summary: 'Delete dashboard' })
  @ApiParam({ name: 'dashboardId', description: 'Dashboard ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteDashboard(@Param('dashboardId') dashboardId: string) {
    await this.dashboardService.deleteDashboard(dashboardId);
  }

  // System Health Endpoints
  @Get('health')
  @ApiOperation({ summary: 'Get comprehensive system health status' })
  @ApiResponse({ status: 200, description: 'System health status retrieved successfully' })
  async getSystemHealth(): Promise<SystemHealth> {
    return await this.metricsService.getSystemHealth();
  }

  @Get('health/services')
  @ApiOperation({ summary: 'Get individual service health status' })
  @ApiResponse({ status: 200, description: 'Service health status retrieved successfully' })
  async getServiceHealth(): Promise<ServiceHealth[]> {
    return await this.metricsService.getServiceHealth();
  }

  @Get('health/dependencies')
  @ApiOperation({ summary: 'Get dependency health status' })
  @ApiResponse({ status: 200, description: 'Dependency health status retrieved successfully' })
  async getDependencyHealth(): Promise<DependencyHealth[]> {
    return await this.metricsService.getDependencyHealth();
  }

  // Real-time Metrics Endpoints
  @Get('realtime/metrics')
  @ApiOperation({ summary: 'Get real-time system metrics' })
  @ApiResponse({ status: 200, description: 'Real-time metrics retrieved successfully' })
  async getRealTimeMetrics(): Promise<{ timestamp: string; apm: PerformanceMetrics; errors: any; alerts: any }> {
    return {
      timestamp: new Date().toISOString(),
      apm: this.apmService.getLatestMetrics(),
      errors: await this.errorTrackingService.getRecentErrors(),
      alerts: await this.alertingService.getActiveAlerts(),
    };
  }

  @Get('reports/business-intelligence')
  @ApiOperation({ summary: 'Get business intelligence reports' })
  @ApiResponse({ status: 200, description: 'Business intelligence reports retrieved successfully' })
  @ApiQuery({ name: 'reportType', required: false, description: 'Type of report to generate' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Time range for report' })
  async getBusinessIntelligenceReports(
    @Query('reportType') reportType?: string,
    @Query('timeRange') timeRange?: string,
  ): Promise<BusinessIntelligenceReport> {
    return await this.dashboardService.generateBusinessIntelligenceReport({ reportType, timeRange });
  }
}