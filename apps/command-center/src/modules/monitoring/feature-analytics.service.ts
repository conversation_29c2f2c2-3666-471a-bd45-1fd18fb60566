import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { FeatureUsage } from './entities/feature-usage.entity';
import { FeatureAdoption } from './entities/feature-adoption.entity';
import { FeatureMetrics } from './entities/feature-metrics.entity';
import { UserEvent } from './entities/user-event.entity';

export interface FeatureUsageData {
  featureId: string;
  featureName: string;
  category: string;
  userId: string;
  sessionId: string;
  action: string;
  timestamp: Date;
  duration?: number;
  success: boolean;
  metadata?: Record<string, any>;
}

export interface FeaturePerformanceMetrics {
  featureName: string;
  totalUsage: number;
  uniqueUsers: number;
  averageDuration: number;
  completionRate: number;
  errorRate: number;
  adoptionRate: number;
  retentionRate: number;
  satisfactionScore?: number;
  performanceTrend: Array<{
    date: Date;
    usage: number;
    performance: number;
  }>;
  userSegments: Array<{
    segment: string;
    usage: number;
    performance: number;
  }>;
}

export interface BusinessIntelligenceReport {
  reportType: string;
  generatedAt: Date;
  timeRange: string;
  summary: {
    totalFeatures: number;
    activeFeatures: number;
    totalUsage: number;
    averageAdoptionRate: number;
    topPerformingFeatures: Array<{
      featureId: string;
      featureName: string;
      usage: number;
      adoptionRate: number;
    }>;
    underperformingFeatures: Array<{
      featureId: string;
      featureName: string;
      usage: number;
      adoptionRate: number;
    }>;
  };
  insights: Array<{
    type: string;
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    recommendation: string;
  }>;
  data: Record<string, any>;
}

@Injectable()
export class FeatureAnalyticsService {
  private readonly logger = new Logger(FeatureAnalyticsService.name);
  private usageBuffer: FeatureUsageData[] = [];
  private flushInterval: NodeJS.Timeout;

  constructor(
    @InjectRepository(FeatureUsage)
    private readonly featureUsageRepository: Repository<FeatureUsage>,
    @InjectRepository(FeatureAdoption)
    private readonly featureAdoptionRepository: Repository<FeatureAdoption>,
    @InjectRepository(FeatureMetrics)
    private readonly featureMetricsRepository: Repository<FeatureMetrics>,
    @InjectRepository(UserEvent)
    private readonly userEventRepository: Repository<UserEvent>,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.startUsageBufferFlush();
  }

  private startUsageBufferFlush() {
    this.flushInterval = setInterval(() => {
      this.flushUsageBuffer();
    }, 10000); // Flush every 10 seconds
  }

  private async flushUsageBuffer() {
    if (this.usageBuffer.length === 0) return;

    try {
      const usageData = [...this.usageBuffer];
      this.usageBuffer = [];

      const usageEntities = usageData.map(data => this.featureUsageRepository.create(data));
      await this.featureUsageRepository.save(usageEntities);

      // Update adoption tracking
      await this.updateFeatureAdoption(usageData);

      this.logger.debug(`Flushed ${usageData.length} feature usage records`);
    } catch (error) {
      this.logger.error('Error flushing usage buffer:', error);
      // Add back to buffer for retry
      this.usageBuffer.unshift(...this.usageBuffer);
    }
  }

  private async updateFeatureAdoption(usageData: FeatureUsageData[]) {
    const adoptionMap = new Map<string, Set<string>>();

    usageData.forEach(data => {
      const key = `${data.featureId}:${data.userId}`;
      if (!adoptionMap.has(data.featureId)) {
        adoptionMap.set(data.featureId, new Set());
      }
      adoptionMap.get(data.featureId)!.add(data.userId);
    });

    for (const [featureId, userIds] of adoptionMap) {
      for (const userId of userIds) {
        const existingAdoption = await this.featureAdoptionRepository.findOne({
          where: { featureId, userId },
        });

        if (!existingAdoption) {
          const adoption = this.featureAdoptionRepository.create({
            featureId,
            userId,
            firstUsage: new Date(),
            lastUsage: new Date(),
            usageCount: 1,
            isActive: true,
          });
          await this.featureAdoptionRepository.save(adoption);
        } else {
          existingAdoption.lastUsage = new Date();
          existingAdoption.usageCount++;
          existingAdoption.isActive = true;
          await this.featureAdoptionRepository.save(existingAdoption);
        }
      }
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  private async calculateFeatureMetrics() {
    try {
      const features = await this.getActiveFeatures();
      
      for (const feature of features) {
        const metrics = await this.calculateFeaturePerformance(feature.featureId);
        
        const existingMetrics = await this.featureMetricsRepository.findOne({
          where: { 
            featureId: feature.featureId,
            date: new Date(new Date().setHours(0, 0, 0, 0)),
          },
        });

        if (existingMetrics) {
          Object.assign(existingMetrics, {
            totalUsage: metrics.totalUsage,
            uniqueUsers: metrics.uniqueUsers,
            completionRate: metrics.completionRate,
            averageDuration: metrics.averageDuration,
            errorRate: metrics.errorRate,
            adoptionRate: metrics.adoptionRate,
            retentionRate: metrics.retentionRate,
            updatedAt: new Date(),
          });
          await this.featureMetricsRepository.save(existingMetrics);
        } else {
          const newMetrics = this.featureMetricsRepository.create({
            featureId: feature.featureId,
            featureName: feature.featureName,
            category: feature.category,
            date: new Date(new Date().setHours(0, 0, 0, 0)),
            totalUsage: metrics.totalUsage,
            uniqueUsers: metrics.uniqueUsers,
            completionRate: metrics.completionRate,
            averageDuration: metrics.averageDuration,
            errorRate: metrics.errorRate,
            adoptionRate: metrics.adoptionRate,
            retentionRate: metrics.retentionRate,
            metadata: {
              performanceTrend: metrics.performanceTrend,
              userSegments: metrics.userSegments,
            },
          });
          await this.featureMetricsRepository.save(newMetrics);
        }
      }

      this.logger.log('Feature metrics calculated and saved');
    } catch (error) {
      this.logger.error('Error calculating feature metrics:', error);
    }
  }

  // Public API methods

  public async trackFeatureUsage(usage: FeatureUsageData): Promise<void> {
    this.usageBuffer.push(usage);
    
    // Emit event for real-time processing
    this.eventEmitter.emit('feature.usage', usage);
  }

  public async getFeatureUsage(filters?: {
    timeRange?: string;
    feature?: string;
    category?: string;
    userId?: string;
  }): Promise<{
    features: FeaturePerformanceMetrics[];
    summary: {
      totalFeatures: number;
      totalUsage: number;
      averageAdoptionRate: number;
    };
  }> {
    const since = filters?.timeRange ? this.parseTimeRange(filters.timeRange) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    
    let query = this.featureUsageRepository
      .createQueryBuilder('usage')
      .where('usage.timestamp >= :since', { since });

    if (filters?.feature) {
      query = query.andWhere('usage.featureId = :featureId', { featureId: filters.feature });
    }

    if (filters?.category) {
      query = query.andWhere('usage.category = :category', { category: filters.category });
    }

    if (filters?.userId) {
      query = query.andWhere('usage.userId = :userId', { userId: filters.userId });
    }

    const usageRecords = await query.getMany();
    const featureMap = new Map<string, FeatureUsageData[]>();

    usageRecords.forEach(record => {
      if (!featureMap.has(record.featureId)) {
        featureMap.set(record.featureId, []);
      }
      featureMap.get(record.featureId)!.push(record);
    });

    const features: FeaturePerformanceMetrics[] = [];
    for (const [featureId, records] of featureMap) {
      const metrics = await this.calculateFeaturePerformanceFromRecords(featureId, records);
      features.push(metrics);
    }

    const totalUsage = usageRecords.length;
    const totalFeatures = features.length;
    const averageAdoptionRate = features.reduce((sum, f) => sum + f.adoptionRate, 0) / totalFeatures || 0;

    return {
      features,
      summary: {
        totalFeatures,
        totalUsage,
        averageAdoptionRate,
      },
    };
  }

  public async getAdoptionMetrics(filters?: {
    timeRange?: string;
    feature?: string;
    category?: string;
  }): Promise<{
    overallAdoption: {
      totalUsers: number;
      adoptedUsers: number;
      adoptionRate: number;
    };
    featureAdoption: Array<{
      featureId: string;
      featureName: string;
      totalUsers: number;
      adoptedUsers: number;
      adoptionRate: number;
      averageTimeToAdopt: number;
    }>;
    adoptionTrends: Array<{
      date: string;
      newAdoptions: number;
      cumulativeAdoptions: number;
    }>;
  }> {
    const since = filters?.timeRange ? this.parseTimeRange(filters.timeRange) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    // Get total unique users
    const totalUsers = await this.userEventRepository
      .createQueryBuilder('event')
      .where('event.timestamp >= :since', { since })
      .select('COUNT(DISTINCT event.userId)', 'count')
      .getRawOne();

    // Get adoption data
    let adoptionQuery = this.featureAdoptionRepository
      .createQueryBuilder('adoption')
      .where('adoption.firstUsage >= :since', { since });

    if (filters?.feature) {
      adoptionQuery = adoptionQuery.andWhere('adoption.featureId = :featureId', { featureId: filters.feature });
    }

    const adoptions = await adoptionQuery.getMany();
    const adoptedUsers = new Set(adoptions.map(a => a.userId)).size;

    // Calculate feature-specific adoption
    const featureAdoption = await this.calculateFeatureAdoptionRates(since, filters?.feature, filters?.category);
    
    // Calculate adoption trends
    const adoptionTrends = await this.calculateAdoptionTrends(since, filters?.feature);

    return {
      overallAdoption: {
        totalUsers: parseInt(totalUsers.count),
        adoptedUsers,
        adoptionRate: (adoptedUsers / parseInt(totalUsers.count)) * 100,
      },
      featureAdoption,
      adoptionTrends,
    };
  }

  public async getPerformanceMetrics(filters?: {
    feature?: string;
    category?: string;
    timeRange?: string;
  }): Promise<{
    performance: FeaturePerformanceMetrics[];
    benchmarks: {
      averageSuccessRate: number;
      averageDuration: number;
      averageErrorRate: number;
    };
  }> {
    const since = filters?.timeRange ? this.parseTimeRange(filters.timeRange) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    
    let query = this.featureMetricsRepository
      .createQueryBuilder('metrics')
      .where('metrics.date >= :since', { since });

    if (filters?.feature) {
      query = query.andWhere('metrics.featureId = :featureId', { featureId: filters.feature });
    }

    if (filters?.category) {
      query = query.andWhere('metrics.category = :category', { category: filters.category });
    }

    const metricsRecords = await query.getMany();
    const performance: FeaturePerformanceMetrics[] = [];

    for (const record of metricsRecords) {
      const trends = await this.getFeatureTrends(record.featureId, since);
      performance.push({
        featureName: record.featureName,
        totalUsage: record.totalUsage,
        uniqueUsers: record.uniqueUsers,
        averageDuration: record.averageDuration,
        completionRate: record.successRate || 0,
        errorRate: record.errorRate,
        adoptionRate: record.adoptionRate,
        retentionRate: record.retentionRate,
        performanceTrend: trends,
        userSegments: [],
      });
    }

    const benchmarks = {
      averageSuccessRate: performance.reduce((sum, p) => sum + p.completionRate, 0) / performance.length || 0,
      averageDuration: performance.reduce((sum, p) => sum + p.averageDuration, 0) / performance.length || 0,
      averageErrorRate: performance.reduce((sum, p) => sum + p.errorRate, 0) / performance.length || 0,
    };

    return { performance, benchmarks };
  }

  public async generateBusinessIntelligenceReport(filters?: {
    reportType?: string;
    timeRange?: string;
  }): Promise<BusinessIntelligenceReport> {
    const timeRange = filters?.timeRange || '30d';
    const since = this.parseTimeRange(timeRange);
    const reportType = filters?.reportType || 'comprehensive';

    // Get basic metrics
    const totalFeatures = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .select('COUNT(DISTINCT usage.featureId)', 'count')
      .where('usage.timestamp >= :since', { since })
      .getRawOne();

    const activeFeatures = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .select('COUNT(DISTINCT usage.featureId)', 'count')
      .where('usage.timestamp >= :since', { since })
      .andWhere('usage.timestamp >= :recent', { recent: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) })
      .getRawOne();

    const totalUsage = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .where('usage.timestamp >= :since', { since })
      .getCount();

    // Get top performing features
    const topPerformingFeatures = await this.getTopPerformingFeatures(since, 10);
    const underperformingFeatures = await this.getUnderperformingFeatures(since, 10);

    const averageAdoptionRate = topPerformingFeatures.reduce((sum, f) => sum + f.adoptionRate, 0) / topPerformingFeatures.length || 0;

    // Generate insights
    const insights = await this.generateInsights(since, reportType);

    // Collect detailed data based on report type
    const data = await this.collectReportData(reportType, since);

    return {
      reportType,
      generatedAt: new Date(),
      timeRange,
      summary: {
        totalFeatures: parseInt(totalFeatures.count),
        activeFeatures: parseInt(activeFeatures.count),
        totalUsage,
        averageAdoptionRate,
        topPerformingFeatures,
        underperformingFeatures,
      },
      insights,
      data,
    };
  }

  // Private helper methods

  private async getActiveFeatures(): Promise<Array<{
    featureId: string;
    featureName: string;
    category: string;
  }>> {
    const features = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .select(['usage.featureId', 'usage.featureName', 'usage.category'])
      .distinctOn(['usage.featureId'])
      .where('usage.timestamp >= :since', { since: new Date(Date.now() - 24 * 60 * 60 * 1000) })
      .getMany();

    return features.map(f => ({
      featureId: f.featureId,
      featureName: f.featureName,
      category: f.category,
    }));
  }

  private async calculateFeaturePerformance(featureId: string): Promise<FeaturePerformanceMetrics> {
    const since = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const records = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .where('usage.featureId = :featureId', { featureId })
      .andWhere('usage.timestamp >= :since', { since })
      .getMany();

    return this.calculateFeaturePerformanceFromRecords(featureId, records);
  }

  private async calculateFeaturePerformanceFromRecords(featureId: string, records: FeatureUsageData[]): Promise<FeaturePerformanceMetrics> {
    const totalUsage = records.length;
    const uniqueUsers = new Set(records.map(r => r.userId)).size;
    const averageUsagePerUser = uniqueUsers > 0 ? totalUsage / uniqueUsers : 0;
    const successfulUsage = records.filter(r => r.success).length;
    const successRate = totalUsage > 0 ? (successfulUsage / totalUsage) * 100 : 0;
    const errorRate = 100 - successRate;
    
    const durationsWithValues = records.filter(r => r.duration != null).map(r => r.duration!);
    const averageDuration = durationsWithValues.length > 0 
      ? durationsWithValues.reduce((sum, d) => sum + d, 0) / durationsWithValues.length 
      : 0;

    // Calculate adoption and retention rates
    const adoptionRate = await this.calculateAdoptionRateForFeature(featureId);
    const retentionRate = await this.calculateRetentionRateForFeature(featureId);

    const trends = await this.getFeatureTrends(featureId, new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));

    return {
      featureName: records[0]?.featureName || 'Unknown',
      totalUsage,
      uniqueUsers,
      averageDuration,
      completionRate: successRate,
      errorRate,
      adoptionRate,
      retentionRate,
      performanceTrend: trends,
      userSegments: [],
    };
  }

  private async calculateAdoptionRateForFeature(featureId: string): Promise<number> {
    const totalUsers = await this.userEventRepository
      .createQueryBuilder('event')
      .select('COUNT(DISTINCT event.userId)', 'count')
      .getRawOne();

    const adoptedUsers = await this.featureAdoptionRepository
      .createQueryBuilder('adoption')
      .where('adoption.featureId = :featureId', { featureId })
      .select('COUNT(DISTINCT adoption.userId)', 'count')
      .getRawOne();

    return parseInt(totalUsers.count) > 0 ? (parseInt(adoptedUsers.count) / parseInt(totalUsers.count)) * 100 : 0;
  }

  private async calculateRetentionRateForFeature(featureId: string): Promise<number> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const earlyUsers = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .where('usage.featureId = :featureId', { featureId })
      .andWhere('usage.timestamp BETWEEN :start AND :end', { start: thirtyDaysAgo, end: sevenDaysAgo })
      .select('COUNT(DISTINCT usage.userId)', 'count')
      .getRawOne();

    const returningUsers = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .where('usage.featureId = :featureId', { featureId })
      .andWhere('usage.timestamp >= :since', { since: sevenDaysAgo })
      .andWhere('usage.userId IN (SELECT DISTINCT userId FROM feature_usage WHERE featureId = :featureId AND timestamp BETWEEN :start AND :end)', 
        { featureId, start: thirtyDaysAgo, end: sevenDaysAgo })
      .select('COUNT(DISTINCT usage.userId)', 'count')
      .getRawOne();

    return parseInt(earlyUsers.count) > 0 ? (parseInt(returningUsers.count) / parseInt(earlyUsers.count)) * 100 : 0;
  }

  private async getFeatureTrends(featureId: string, since: Date): Promise<Array<{
    date: string;
    usage: number;
    uniqueUsers: number;
  }>> {
    const trends = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .select([
        'DATE(usage.timestamp) as date',
        'COUNT(*) as usage',
        'COUNT(DISTINCT usage.userId) as unique_users'
      ])
      .where('usage.featureId = :featureId', { featureId })
      .andWhere('usage.timestamp >= :since', { since })
      .groupBy('DATE(usage.timestamp)')
      .orderBy('date', 'ASC')
      .getRawMany();

    return trends.map(trend => ({
      date: trend.date,
      usage: parseInt(trend.usage),
      uniqueUsers: parseInt(trend.unique_users),
    }));
  }

  private async calculateFeatureAdoptionRates(since: Date, feature?: string, category?: string): Promise<Array<{
    featureId: string;
    featureName: string;
    totalUsers: number;
    adoptedUsers: number;
    adoptionRate: number;
    averageTimeToAdopt: number;
  }>> {
    // Implementation would calculate adoption rates for each feature
    // For now, return placeholder
    return [];
  }

  private async calculateAdoptionTrends(since: Date, feature?: string): Promise<Array<{
    date: string;
    newAdoptions: number;
    cumulativeAdoptions: number;
  }>> {
    // Implementation would calculate adoption trends over time
    // For now, return placeholder
    return [];
  }

  private async getTopPerformingFeatures(since: Date, limit: number): Promise<Array<{
    featureId: string;
    featureName: string;
    usage: number;
    adoptionRate: number;
  }>> {
    const features = await this.featureMetricsRepository
      .createQueryBuilder('metrics')
      .where('metrics.date >= :since', { since })
      .orderBy('metrics.totalUsage', 'DESC')
      .limit(limit)
      .getMany();

    return features.map(f => ({
      featureId: f.featureId,
      featureName: f.featureName,
      usage: f.totalUsage,
      adoptionRate: f.adoptionRate,
    }));
  }

  private async getUnderperformingFeatures(since: Date, limit: number): Promise<Array<{
    featureId: string;
    featureName: string;
    usage: number;
    adoptionRate: number;
  }>> {
    const features = await this.featureMetricsRepository
      .createQueryBuilder('metrics')
      .where('metrics.date >= :since', { since })
      .orderBy('metrics.adoptionRate', 'ASC')
      .limit(limit)
      .getMany();

    return features.map(f => ({
      featureId: f.featureId,
      featureName: f.featureName,
      usage: f.totalUsage,
      adoptionRate: f.adoptionRate,
    }));
  }

  private async generateInsights(since: Date, reportType: string): Promise<Array<{
    type: string;
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    recommendation: string;
  }>> {
    const insights = [];

    // Analyze feature adoption trends
    const lowAdoptionFeatures = await this.getUnderperformingFeatures(since, 5);
    if (lowAdoptionFeatures.length > 0) {
      insights.push({
        type: 'adoption',
        title: 'Low Feature Adoption Detected',
        description: `${lowAdoptionFeatures.length} features have adoption rates below 10%`,
        impact: 'high' as const,
        recommendation: 'Consider user onboarding improvements, feature discoverability, or sunset unused features',
      });
    }

    // Analyze usage patterns
    const recentUsage = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .where('usage.timestamp >= :recent', { recent: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) })
      .getCount();

    const previousUsage = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .where('usage.timestamp BETWEEN :start AND :end', {
        start: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
        end: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      })
      .getCount();

    const usageChange = previousUsage > 0 ? ((recentUsage - previousUsage) / previousUsage) * 100 : 0;

    if (usageChange < -20) {
      insights.push({
        type: 'usage',
        title: 'Significant Usage Decline',
        description: `Overall feature usage has declined by ${Math.abs(usageChange).toFixed(1)}% in the past week`,
        impact: 'high' as const,
        recommendation: 'Investigate potential issues, user feedback, or competing features',
      });
    } else if (usageChange > 20) {
      insights.push({
        type: 'usage',
        title: 'Strong Usage Growth',
        description: `Overall feature usage has increased by ${usageChange.toFixed(1)}% in the past week`,
        impact: 'medium' as const,
        recommendation: 'Monitor performance and scale resources if needed',
      });
    }

    return insights;
  }

  private async collectReportData(reportType: string, since: Date): Promise<Record<string, any>> {
    const data: Record<string, any> = {};

    if (reportType === 'comprehensive' || reportType === 'usage') {
      data.usageByCategory = await this.getUsageByCategory(since);
      data.usageByTimeOfDay = await this.getUsageByTimeOfDay(since);
      data.featureCorrelations = await this.getFeatureCorrelations(since);
    }

    if (reportType === 'comprehensive' || reportType === 'performance') {
      data.performanceMetrics = await this.getPerformanceMetrics({ timeRange: '30d' });
      data.errorAnalysis = await this.getErrorAnalysis(since);
    }

    if (reportType === 'comprehensive' || reportType === 'adoption') {
      data.adoptionFunnels = await this.getAdoptionFunnels(since);
      data.userSegmentation = await this.getUserSegmentation(since);
    }

    return data;
  }

  private async getUsageByCategory(since: Date): Promise<Record<string, number>> {
    const usage = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .select(['usage.category', 'COUNT(*) as count'])
      .where('usage.timestamp >= :since', { since })
      .groupBy('usage.category')
      .getRawMany();

    return usage.reduce((acc, item) => {
      acc[item.category] = parseInt(item.count);
      return acc;
    }, {} as Record<string, number>);
  }

  private async getUsageByTimeOfDay(since: Date): Promise<Array<{
    hour: number;
    usage: number;
  }>> {
    const usage = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .select(['EXTRACT(HOUR FROM usage.timestamp) as hour', 'COUNT(*) as usage'])
      .where('usage.timestamp >= :since', { since })
      .groupBy('EXTRACT(HOUR FROM usage.timestamp)')
      .orderBy('hour', 'ASC')
      .getRawMany();

    return usage.map(item => ({
      hour: parseInt(item.hour),
      usage: parseInt(item.usage),
    }));
  }

  private async getFeatureCorrelations(since: Date): Promise<Array<{
    feature1: string;
    feature2: string;
    correlation: number;
  }>> {
    // Implementation would calculate feature usage correlations
    // For now, return placeholder
    return [];
  }

  private async getErrorAnalysis(since: Date): Promise<{
    totalErrors: number;
    errorRate: number;
    topErrors: Array<{
      featureId: string;
      errorCount: number;
      errorRate: number;
    }>;
  }> {
    const totalUsage = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .where('usage.timestamp >= :since', { since })
      .getCount();

    const totalErrors = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .where('usage.timestamp >= :since', { since })
      .andWhere('usage.success = false')
      .getCount();

    const errorRate = totalUsage > 0 ? (totalErrors / totalUsage) * 100 : 0;

    const topErrors = await this.featureUsageRepository
      .createQueryBuilder('usage')
      .select([
        'usage.featureId',
        'COUNT(CASE WHEN usage.success = false THEN 1 END) as error_count',
        'COUNT(*) as total_count'
      ])
      .where('usage.timestamp >= :since', { since })
      .groupBy('usage.featureId')
      .having('COUNT(CASE WHEN usage.success = false THEN 1 END) > 0')
      .orderBy('error_count', 'DESC')
      .limit(10)
      .getRawMany();

    return {
      totalErrors,
      errorRate,
      topErrors: topErrors.map(item => ({
        featureId: item.featureId,
        errorCount: parseInt(item.error_count),
        errorRate: (parseInt(item.error_count) / parseInt(item.total_count)) * 100,
      })),
    };
  }

  private async getAdoptionFunnels(since: Date): Promise<Array<{
    funnel: string;
    steps: Array<{
      step: string;
      users: number;
      conversionRate: number;
    }>;
  }>> {
    // Implementation would calculate adoption funnels
    // For now, return placeholder
    return [];
  }

  private async getUserSegmentation(since: Date): Promise<{
    segments: Array<{
      segment: string;
      users: number;
      features: Array<{
        featureId: string;
        usage: number;
      }>;
    }>;
  }> {
    // Implementation would calculate user segmentation
    // For now, return placeholder
    return { segments: [] };
  }

  private parseTimeRange(timeRange: string): Date {
    const now = new Date();
    const match = timeRange.match(/(\d+)([hdwm])/);
    
    if (!match) return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const value = parseInt(match[1]);
    const unit = match[2];
    
    switch (unit) {
      case 'h': return new Date(now.getTime() - value * 60 * 60 * 1000);
      case 'd': return new Date(now.getTime() - value * 24 * 60 * 60 * 1000);
      case 'w': return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000);
      case 'm': return new Date(now.getTime() - value * 30 * 24 * 60 * 60 * 1000);
      default: return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
  }
}