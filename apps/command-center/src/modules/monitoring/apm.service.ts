import { Injectable, OnM<PERSON>uleInit, On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { promisify } from 'util';
import * as fs from 'fs';
import * as os from 'os';
import { 
  trace, 
  context, 
  metrics, 
  SpanKind, 
  SpanStatusCode, 
  Tracer, 
  Span, 
  Meter 
} from '@opentelemetry/api';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-grpc';
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-grpc';
import { PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics';
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base';

export interface PerformanceMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    heap: {
      used: number;
      total: number;
    };
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  disk: {
    readBytes: number;
    writeBytes: number;
    readOps: number;
    writeOps: number;
  };
  database: {
    activeConnections: number;
    queryTime: number;
    queryCount: number;
  };
  redis: {
    usedMemory: number;
    connectedClients: number;
    commandsProcessed: number;
  };
  application: {
    activeRequests: number;
    responseTime: number;
    errorRate: number;
    throughput: number;
  };
}

export interface AlertThreshold {
  metric: string;
  value: number;
  comparison: 'gt' | 'lt' | 'eq';
  severity: 'critical' | 'warning' | 'info';
  enabled: boolean;
}

@Injectable()
export class ApmService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ApmService.name);
  private tracer: Tracer;
  private meter: Meter;
  private sdk: NodeSDK;
  private performanceMetrics: PerformanceMetrics[] = [];
  private alertThresholds: AlertThreshold[] = [];
  private activeSpans: Map<string, Span> = new Map();
  private lastNetworkStats: any = null;
  private lastDiskStats: any = null;

  // Metrics
  private requestDurationHistogram: any;
  private requestCounter: any;
  private errorCounter: any;
  private cpuUsageGauge: any;
  private memoryUsageGauge: any;
  private diskUsageGauge: any;
  private databaseConnectionsGauge: any;
  private redisMemoryGauge: any;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    await this.initializeOpenTelemetry();
    this.initializeMetrics();
    this.initializeAlertThresholds();
    this.startPerformanceMonitoring();
  }

  async onModuleDestroy() {
    if (this.sdk) {
      await this.sdk.shutdown();
    }
  }

  private async initializeOpenTelemetry() {
    const serviceName = this.configService.get<string>('SERVICE_NAME', 'luminar-command-center');
    const otelEndpoint = this.configService.get<string>('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://localhost:4317');
    const environment = this.configService.get<string>('NODE_ENV', 'development');

    const resource = new Resource({
      [SemanticResourceAttributes.SERVICE_NAME]: serviceName,
      [SemanticResourceAttributes.SERVICE_VERSION]: process.env.npm_package_version || '1.0.0',
      [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: environment,
    });

    const traceExporter = new OTLPTraceExporter({
      url: `${otelEndpoint}/v1/traces`,
    });

    const metricExporter = new OTLPMetricExporter({
      url: `${otelEndpoint}/v1/metrics`,
    });

    this.sdk = new NodeSDK({
      resource,
      traceExporter,
      spanProcessor: new BatchSpanProcessor(traceExporter),
      metricReader: new PeriodicExportingMetricReader({
        exporter: metricExporter as any,
        exportIntervalMillis: 5000,
      }) as any,
      instrumentations: [
        getNodeAutoInstrumentations({
          '@opentelemetry/instrumentation-fs': {
            enabled: false,
          },
          '@opentelemetry/instrumentation-http': {
            enabled: true,
            requestHook: (span, request) => {
              span.setAttributes({
                'http.request.body.size': (request as any).headers?.['content-length'] || 0,
                'http.user_agent': (request as any).headers?.['user-agent'] || 'unknown',
              });
            },
            responseHook: (span, response) => {
              span.setAttributes({
                'http.response.body.size': (response as any).headers?.['content-length'] || 0,
              });
            },
          },
          '@opentelemetry/instrumentation-express': {
            enabled: true,
          },
          '@opentelemetry/instrumentation-nestjs-core': {
            enabled: true,
          },
        }),
      ],
    });

    await this.sdk.start();

    this.tracer = trace.getTracer(serviceName, process.env.npm_package_version || '1.0.0');
    this.meter = metrics.getMeter(serviceName, process.env.npm_package_version || '1.0.0');

    this.logger.log('OpenTelemetry APM initialized');
  }

  private initializeMetrics() {
    this.requestDurationHistogram = this.meter.createHistogram('http_request_duration_seconds', {
      description: 'HTTP request duration in seconds',
      unit: 's',
    });

    this.requestCounter = this.meter.createCounter('http_requests_total', {
      description: 'Total number of HTTP requests',
    });

    this.errorCounter = this.meter.createCounter('http_errors_total', {
      description: 'Total number of HTTP errors',
    });

    this.cpuUsageGauge = this.meter.createObservableGauge('cpu_usage_percent', {
      description: 'CPU usage percentage',
      unit: '%',
    });

    this.memoryUsageGauge = this.meter.createObservableGauge('memory_usage_bytes', {
      description: 'Memory usage in bytes',
      unit: 'bytes',
    });

    this.diskUsageGauge = this.meter.createObservableGauge('disk_usage_bytes', {
      description: 'Disk usage in bytes',
      unit: 'bytes',
    });

    this.databaseConnectionsGauge = this.meter.createObservableGauge('database_connections_active', {
      description: 'Active database connections',
    });

    this.redisMemoryGauge = this.meter.createObservableGauge('redis_memory_used_bytes', {
      description: 'Redis memory usage in bytes',
      unit: 'bytes',
    });

    // Set up observable callbacks
    this.cpuUsageGauge.addCallback((result) => {
      const usage = process.cpuUsage();
      const cpuPercent = (usage.user + usage.system) / 1000000; // Convert to seconds
      result.observe(cpuPercent);
    });

    this.memoryUsageGauge.addCallback((result) => {
      const memUsage = process.memoryUsage();
      result.observe(memUsage.rss, { type: 'rss' });
      result.observe(memUsage.heapUsed, { type: 'heap_used' });
      result.observe(memUsage.heapTotal, { type: 'heap_total' });
    });
  }

  private initializeAlertThresholds() {
    this.alertThresholds = [
      {
        metric: 'cpu.usage',
        value: 80,
        comparison: 'gt',
        severity: 'warning',
        enabled: true,
      },
      {
        metric: 'cpu.usage',
        value: 95,
        comparison: 'gt',
        severity: 'critical',
        enabled: true,
      },
      {
        metric: 'memory.usage',
        value: 85,
        comparison: 'gt',
        severity: 'warning',
        enabled: true,
      },
      {
        metric: 'memory.usage',
        value: 95,
        comparison: 'gt',
        severity: 'critical',
        enabled: true,
      },
      {
        metric: 'application.errorRate',
        value: 5,
        comparison: 'gt',
        severity: 'warning',
        enabled: true,
      },
      {
        metric: 'application.responseTime',
        value: 2000,
        comparison: 'gt',
        severity: 'warning',
        enabled: true,
      },
      {
        metric: 'database.activeConnections',
        value: 50,
        comparison: 'gt',
        severity: 'warning',
        enabled: true,
      },
    ];
  }

  private startPerformanceMonitoring() {
    setInterval(() => {
      this.collectPerformanceMetrics();
    }, 30000); // Collect every 30 seconds
  }

  @Cron(CronExpression.EVERY_MINUTE)
  private async collectPerformanceMetrics() {
    try {
      const metrics = await this.gatherSystemMetrics();
      this.performanceMetrics.push(metrics);
      
      // Keep only last 60 minutes of data
      if (this.performanceMetrics.length > 120) {
        this.performanceMetrics = this.performanceMetrics.slice(-120);
      }

      // Check thresholds and emit alerts
      this.checkAlertThresholds(metrics);

      // Emit metrics event
      this.eventEmitter.emit('metrics.collected', metrics);
    } catch (error) {
      this.logger.error('Error collecting performance metrics:', error);
    }
  }

  private async gatherSystemMetrics(): Promise<PerformanceMetrics> {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const loadAverage = os.loadavg();

    return {
      timestamp: new Date().toISOString(),
      cpu: {
        usage: await this.getCpuUsage(),
        loadAverage,
      },
      memory: {
        used: memUsage.rss,
        total: os.totalmem(),
        heap: {
          used: memUsage.heapUsed,
          total: memUsage.heapTotal,
        },
      },
      network: await this.getNetworkStats(),
      disk: await this.getDiskStats(),
      database: await this.getDatabaseStats(),
      redis: await this.getRedisStats(),
      application: await this.getApplicationStats(),
    };
  }

  private async getCpuUsage(): Promise<number> {
    const startUsage = process.cpuUsage();
    await new Promise(resolve => setTimeout(resolve, 100));
    const endUsage = process.cpuUsage(startUsage);
    const totalUsage = endUsage.user + endUsage.system;
    return (totalUsage / 100000) * 100; // Convert to percentage
  }

  private async getNetworkStats(): Promise<any> {
    try {
      const stats = await fs.promises.readFile('/proc/net/dev', 'utf8');
      const lines = stats.split('\n').slice(2);
      let bytesIn = 0, bytesOut = 0, packetsIn = 0, packetsOut = 0;

      lines.forEach(line => {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 10 && !parts[0].startsWith('lo:')) {
          bytesIn += parseInt(parts[1]) || 0;
          packetsIn += parseInt(parts[2]) || 0;
          bytesOut += parseInt(parts[9]) || 0;
          packetsOut += parseInt(parts[10]) || 0;
        }
      });

      return { bytesIn, bytesOut, packetsIn, packetsOut };
    } catch (error) {
      return { bytesIn: 0, bytesOut: 0, packetsIn: 0, packetsOut: 0 };
    }
  }

  private async getDiskStats(): Promise<any> {
    try {
      const stats = await fs.promises.readFile('/proc/diskstats', 'utf8');
      const lines = stats.split('\n');
      let readBytes = 0, writeBytes = 0, readOps = 0, writeOps = 0;

      lines.forEach(line => {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 14) {
          readOps += parseInt(parts[3]) || 0;
          readBytes += parseInt(parts[5]) * 512 || 0; // Convert sectors to bytes
          writeOps += parseInt(parts[7]) || 0;
          writeBytes += parseInt(parts[9]) * 512 || 0; // Convert sectors to bytes
        }
      });

      return { readBytes, writeBytes, readOps, writeOps };
    } catch (error) {
      return { readBytes: 0, writeBytes: 0, readOps: 0, writeOps: 0 };
    }
  }

  private async getDatabaseStats(): Promise<any> {
    // This would integrate with your database monitoring
    return {
      activeConnections: 0,
      queryTime: 0,
      queryCount: 0,
    };
  }

  private async getRedisStats(): Promise<any> {
    // This would integrate with your Redis monitoring
    return {
      usedMemory: 0,
      connectedClients: 0,
      commandsProcessed: 0,
    };
  }

  private async getApplicationStats(): Promise<any> {
    // This would track application-specific metrics
    return {
      activeRequests: this.activeSpans.size,
      responseTime: 0,
      errorRate: 0,
      throughput: 0,
    };
  }

  private checkAlertThresholds(metrics: PerformanceMetrics) {
    this.alertThresholds.forEach(threshold => {
      if (!threshold.enabled) return;

      const value = this.getMetricValue(metrics, threshold.metric);
      const shouldAlert = this.evaluateThreshold(value, threshold.value, threshold.comparison);

      if (shouldAlert) {
        this.eventEmitter.emit('alert.triggered', {
          metric: threshold.metric,
          value,
          threshold: threshold.value,
          severity: threshold.severity,
          timestamp: new Date().toISOString(),
        });
      }
    });
  }

  private getMetricValue(metrics: PerformanceMetrics, path: string): number {
    const parts = path.split('.');
    let value: any = metrics;
    
    for (const part of parts) {
      value = value[part];
      if (value === undefined) return 0;
    }
    
    return typeof value === 'number' ? value : 0;
  }

  private evaluateThreshold(value: number, threshold: number, comparison: string): boolean {
    switch (comparison) {
      case 'gt': return value > threshold;
      case 'lt': return value < threshold;
      case 'eq': return value === threshold;
      default: return false;
    }
  }

  // Public API methods
  
  public startSpan(name: string, attributes?: Record<string, any>): Span {
    const span = this.tracer.startSpan(name, {
      kind: SpanKind.INTERNAL,
      attributes,
    });
    
    const spanId = span.spanContext().spanId;
    this.activeSpans.set(spanId, span);
    
    return span;
  }

  public async withSpan<T>(
    name: string,
    fn: (span: Span) => Promise<T>,
    attributes?: Record<string, any>,
  ): Promise<T> {
    const span = this.startSpan(name, attributes);
    
    try {
      const result = await context.with(trace.setSpan(context.active(), span), () => fn(span));
      span.setStatus({ code: SpanStatusCode.OK });
      return result;
    } catch (error) {
      span.setStatus({ code: SpanStatusCode.ERROR, message: error.message });
      span.recordException(error);
      throw error;
    } finally {
      span.end();
      this.activeSpans.delete(span.spanContext().spanId);
    }
  }

  public recordHttpRequest(method: string, route: string, statusCode: number, duration: number) {
    const labels = { method, route, status_code: statusCode.toString() };
    
    this.requestCounter.add(1, labels);
    this.requestDurationHistogram.record(duration / 1000, labels);
    
    if (statusCode >= 400) {
      this.errorCounter.add(1, labels);
    }
  }

  public getPerformanceMetrics(): PerformanceMetrics[] {
    return this.performanceMetrics;
  }

  public getLatestMetrics(): PerformanceMetrics | null {
    return this.performanceMetrics[this.performanceMetrics.length - 1] || null;
  }

  public updateAlertThreshold(metric: string, value: number, comparison: 'gt' | 'lt' | 'eq', severity: 'critical' | 'warning' | 'info') {
    const threshold = this.alertThresholds.find(t => t.metric === metric && t.severity === severity);
    if (threshold) {
      threshold.value = value;
      threshold.comparison = comparison;
    } else {
      this.alertThresholds.push({ metric, value, comparison, severity, enabled: true });
    }
  }

  public getAlertThresholds(): AlertThreshold[] {
    return this.alertThresholds;
  }
}