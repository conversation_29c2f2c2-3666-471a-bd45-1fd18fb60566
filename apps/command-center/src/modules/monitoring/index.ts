// Export all monitoring module services
export { ApmService } from './apm.service';
export { ErrorTrackingService } from './error-tracking.service';
export { UserAnalyticsService } from './user-analytics.service';
export { FeatureAnalyticsService } from './feature-analytics.service';
export { AlertingService } from './alerting.service';
export { MetricsService } from './metrics.service';
export { DashboardService } from './dashboard.service';
export { MonitoringGateway } from './monitoring.gateway';
export { MonitoringModule } from './monitoring.module';

// Export all types
export * from './types';

// Export entities if needed by other modules
export { ErrorEvent } from './entities/error-event.entity';
export { ErrorGroup } from './entities/error-group.entity';
export { ErrorResolution } from './entities/error-resolution.entity';
export { UserEvent } from './entities/user-event.entity';
export { UserSession } from './entities/user-session.entity';
export { UserMetrics } from './entities/user-metrics.entity';
export { ConsentRecord as MonitoringConsentRecord } from './entities/consent-record.entity';
export { FeatureUsage } from './entities/feature-usage.entity';
export { FeatureAdoption } from './entities/feature-adoption.entity';
export { FeatureMetrics } from './entities/feature-metrics.entity';
export { Alert } from './entities/alert.entity';
export { NotificationChannel } from './entities/notification-channel.entity';
export { AlertRule } from './entities/alert-rule.entity';
export { Dashboard } from './entities/dashboard.entity';
export { DashboardWidget } from './entities/dashboard-widget.entity';