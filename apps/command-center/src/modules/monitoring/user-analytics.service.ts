import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { UserEvent } from './entities/user-event.entity';
import { UserSession } from './entities/user-session.entity';
import { UserMetrics } from './entities/user-metrics.entity';
import { ConsentRecord } from './entities/consent-record.entity';

export interface UserAnalyticsEvent {
  userId: string;
  sessionId: string;
  eventType: string;
  eventName: string;
  properties?: Record<string, any>;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
  url?: string;
  referrer?: string;
  platform?: string;
  country?: string;
  city?: string;
}

export interface UserBehaviorMetrics {
  activeUsers: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  sessions: {
    total: number;
    averageDuration: number;
    bounceRate: number;
    averagePageViews: number;
  };
  engagement: {
    dailyActiveUsers: number;
    weeklyActiveUsers: number;
    monthlyActiveUsers: number;
    stickyFactor: number;
  };
  retention: {
    day1: number;
    day7: number;
    day30: number;
  };
  demographics: {
    countries: Record<string, number>;
    platforms: Record<string, number>;
    devices: Record<string, number>;
  };
  funnels: Array<{
    step: string;
    users: number;
    conversionRate: number;
  }>;
}

export interface PrivacySettings {
  enableTracking: boolean;
  enablePersonalization: boolean;
  enableAnalytics: boolean;
  dataRetentionDays: number;
  anonymizeIp: boolean;
  respectDoNotTrack: boolean;
}

@Injectable()
export class UserAnalyticsService implements OnModuleInit {
  private readonly logger = new Logger(UserAnalyticsService.name);
  private privacySettings: PrivacySettings;
  private eventBuffer: UserAnalyticsEvent[] = [];
  private sessionCache: Map<string, UserSession> = new Map();
  private flushInterval: NodeJS.Timeout;

  constructor(
    @InjectRepository(UserEvent)
    private readonly userEventRepository: Repository<UserEvent>,
    @InjectRepository(UserSession)
    private readonly userSessionRepository: Repository<UserSession>,
    @InjectRepository(UserMetrics)
    private readonly userMetricsRepository: Repository<UserMetrics>,
    @InjectRepository(ConsentRecord)
    private readonly consentRecordRepository: Repository<ConsentRecord>,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    await this.initializePrivacySettings();
    this.startEventBufferFlush();
  }

  private async initializePrivacySettings() {
    this.privacySettings = {
      enableTracking: this.configService.get<boolean>('ANALYTICS_ENABLE_TRACKING', true),
      enablePersonalization: this.configService.get<boolean>('ANALYTICS_ENABLE_PERSONALIZATION', true),
      enableAnalytics: this.configService.get<boolean>('ANALYTICS_ENABLE_ANALYTICS', true),
      dataRetentionDays: this.configService.get<number>('ANALYTICS_DATA_RETENTION_DAYS', 365),
      anonymizeIp: this.configService.get<boolean>('ANALYTICS_ANONYMIZE_IP', true),
      respectDoNotTrack: this.configService.get<boolean>('ANALYTICS_RESPECT_DNT', true),
    };

    this.logger.log('User analytics privacy settings initialized');
  }

  private startEventBufferFlush() {
    this.flushInterval = setInterval(() => {
      this.flushEventBuffer();
    }, 5000); // Flush every 5 seconds
  }

  private async flushEventBuffer() {
    if (this.eventBuffer.length === 0) return;

    try {
      const events = [...this.eventBuffer];
      this.eventBuffer = [];
      
      const eventEntities = events.map(event => this.userEventRepository.create({
        userId: event.userId,
        sessionId: event.sessionId,
        eventType: event.eventType,
        eventName: event.eventName,
        properties: event.properties,
        timestamp: event.timestamp,
        userAgent: event.userAgent,
        ip: this.privacySettings.anonymizeIp ? this.anonymizeIp(event.ip) : event.ip,
        url: event.url,
        referrer: event.referrer,
        platform: event.platform,
        country: event.country,
        city: event.city,
      }));

      await this.userEventRepository.save(eventEntities);
      await this.updateSessionMetrics(events);
      
      this.logger.debug(`Flushed ${events.length} user events`);
    } catch (error) {
      this.logger.error('Error flushing event buffer:', error);
      // Add events back to buffer for retry
      this.eventBuffer.unshift(...this.eventBuffer);
    }
  }

  private async updateSessionMetrics(events: UserAnalyticsEvent[]) {
    const sessionUpdates = new Map<string, {
      lastActivity: Date;
      eventCount: number;
      pageViews: number;
    }>();

    events.forEach(event => {
      const sessionId = event.sessionId;
      if (!sessionUpdates.has(sessionId)) {
        sessionUpdates.set(sessionId, {
          lastActivity: event.timestamp,
          eventCount: 0,
          pageViews: 0,
        });
      }

      const update = sessionUpdates.get(sessionId)!;
      update.eventCount++;
      if (event.eventType === 'page_view') {
        update.pageViews++;
      }
      if (event.timestamp > update.lastActivity) {
        update.lastActivity = event.timestamp;
      }
    });

    for (const [sessionId, update] of sessionUpdates) {
      let session = this.sessionCache.get(sessionId);
      if (!session) {
        session = await this.userSessionRepository.findOne({
          where: { sessionId },
        });
        if (session) {
          this.sessionCache.set(sessionId, session);
        }
      }

      if (session) {
        session.lastActivity = update.lastActivity;
        session.eventCount += update.eventCount;
        session.pageViews += update.pageViews;
        session.duration = Math.floor((update.lastActivity.getTime() - session.startTime.getTime()) / 1000);
        
        await this.userSessionRepository.save(session);
      }
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  private async calculateUserMetrics() {
    try {
      const now = new Date();
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const metrics = await this.calculateBehaviorMetrics();
      
      const userMetrics = this.userMetricsRepository.create({
        date: todayStart,
        dailyActiveUsers: metrics.activeUsers.daily,
        weeklyActiveUsers: metrics.activeUsers.weekly,
        monthlyActiveUsers: metrics.activeUsers.monthly,
        totalSessions: metrics.sessions.total,
        averageSessionDuration: metrics.sessions.averageDuration,
        bounceRate: metrics.sessions.bounceRate,
        retentionDay1: metrics.retention.day1,
        retentionDay7: metrics.retention.day7,
        retentionDay30: metrics.retention.day30,
        metadata: {
          demographics: metrics.demographics,
          engagement: metrics.engagement,
        },
      });

      await this.userMetricsRepository.save(userMetrics);
      
      this.logger.log('User metrics calculated and saved');
    } catch (error) {
      this.logger.error('Error calculating user metrics:', error);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  private async cleanupOldData() {
    if (!this.privacySettings.dataRetentionDays) return;

    const cutoffDate = new Date(Date.now() - this.privacySettings.dataRetentionDays * 24 * 60 * 60 * 1000);
    
    try {
      await this.userEventRepository
        .createQueryBuilder()
        .delete()
        .where('timestamp < :cutoff', { cutoff: cutoffDate })
        .execute();

      await this.userSessionRepository
        .createQueryBuilder()
        .delete()
        .where('startTime < :cutoff', { cutoff: cutoffDate })
        .execute();

      this.logger.log(`Cleaned up user data older than ${this.privacySettings.dataRetentionDays} days`);
    } catch (error) {
      this.logger.error('Error cleaning up old data:', error);
    }
  }

  // Public API methods

  public async trackEvent(event: UserAnalyticsEvent): Promise<void> {
    // Check if tracking is enabled
    if (!this.privacySettings.enableTracking) {
      return;
    }

    // Check user consent
    const hasConsent = await this.checkUserConsent(event.userId);
    if (!hasConsent) {
      return;
    }

    // Check Do Not Track header
    if (this.privacySettings.respectDoNotTrack && event.properties?.doNotTrack) {
      return;
    }

    // Add to buffer for batch processing
    this.eventBuffer.push(event);

    // Update or create session
    await this.updateUserSession(event);

    // Emit event for real-time processing
    this.eventEmitter.emit('user.event', event);
  }

  public async startSession(userId: string, sessionId: string, metadata: {
    userAgent?: string;
    ip?: string;
    platform?: string;
    country?: string;
    city?: string;
    referrer?: string;
  }): Promise<void> {
    const session = this.userSessionRepository.create({
      userId,
      sessionId,
      startTime: new Date(),
      lastActivity: new Date(),
      userAgent: metadata.userAgent,
      ip: this.privacySettings.anonymizeIp ? this.anonymizeIp(metadata.ip) : metadata.ip,
      platform: metadata.platform,
      country: metadata.country,
      city: metadata.city,
      referrer: metadata.referrer,
      eventCount: 0,
      pageViews: 0,
      duration: 0,
      isActive: true,
    });

    await this.userSessionRepository.save(session);
    this.sessionCache.set(sessionId, session);
  }

  public async endSession(sessionId: string): Promise<void> {
    const session = await this.userSessionRepository.findOne({
      where: { sessionId },
    });

    if (session) {
      session.isActive = false;
      session.endTime = new Date();
      session.duration = Math.floor((session.endTime.getTime() - session.startTime.getTime()) / 1000);
      
      await this.userSessionRepository.save(session);
      this.sessionCache.delete(sessionId);
    }
  }

  public async recordConsent(userId: string, consentType: string, granted: boolean): Promise<void> {
    const consent = this.consentRecordRepository.create({
      userId,
      consentType,
      granted,
      timestamp: new Date(),
    });

    await this.consentRecordRepository.save(consent);
  }

  public async getAnalytics(filters?: {
    timeRange?: string;
    metric?: string;
    userId?: string;
  }): Promise<UserBehaviorMetrics> {
    return await this.calculateBehaviorMetrics(filters);
  }

  public async getBehaviorPatterns(filters?: {
    timeRange?: string;
    userId?: string;
  }): Promise<{
    commonPaths: Array<{
      path: string;
      count: number;
      avgDuration: number;
    }>;
    dropOffPoints: Array<{
      page: string;
      dropOffRate: number;
    }>;
    engagementPatterns: Array<{
      hour: number;
      activeUsers: number;
      events: number;
    }>;
  }> {
    const since = filters?.timeRange ? this.parseTimeRange(filters.timeRange) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    
    // Calculate common user paths
    const commonPaths = await this.calculateCommonPaths(since, filters?.userId);
    
    // Calculate drop-off points
    const dropOffPoints = await this.calculateDropOffPoints(since, filters?.userId);
    
    // Calculate engagement patterns by hour
    const engagementPatterns = await this.calculateEngagementPatterns(since, filters?.userId);

    return {
      commonPaths,
      dropOffPoints,
      engagementPatterns,
    };
  }

  public async getRetentionMetrics(filters?: {
    cohort?: string;
    userId?: string;
  }): Promise<{
    cohortAnalysis: Array<{
      cohort: string;
      users: number;
      retention: Record<string, number>;
    }>;
    overallRetention: {
      day1: number;
      day7: number;
      day30: number;
    };
  }> {
    const cohortPeriod = filters?.cohort || 'weekly';
    
    const cohortAnalysis = await this.calculateCohortAnalysis(cohortPeriod, filters?.userId);
    const overallRetention = await this.calculateOverallRetention(filters?.userId);

    return {
      cohortAnalysis,
      overallRetention,
    };
  }

  public async getFunnelAnalysis(funnelSteps: string[], filters?: {
    timeRange?: string;
    userId?: string;
  }): Promise<Array<{
    step: string;
    users: number;
    conversionRate: number;
    dropOffRate: number;
  }>> {
    const since = filters?.timeRange ? this.parseTimeRange(filters.timeRange) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    
    const funnelResults = [];
    let previousStepUsers = 0;

    for (let i = 0; i < funnelSteps.length; i++) {
      const step = funnelSteps[i];
      const stepUsers = await this.userEventRepository
        .createQueryBuilder('event')
        .where('event.eventName = :eventName', { eventName: step })
        .andWhere('event.timestamp >= :since', { since })
        .distinctOn(['event.userId'])
        .getCount();

      const conversionRate = i === 0 ? 100 : previousStepUsers > 0 ? (stepUsers / previousStepUsers) * 100 : 0;
      const dropOffRate = i === 0 ? 0 : previousStepUsers > 0 ? ((previousStepUsers - stepUsers) / previousStepUsers) * 100 : 0;

      funnelResults.push({
        step,
        users: stepUsers,
        conversionRate,
        dropOffRate,
      });

      previousStepUsers = stepUsers;
    }

    return funnelResults;
  }

  // Private helper methods

  private async updateUserSession(event: UserAnalyticsEvent) {
    let session = this.sessionCache.get(event.sessionId);
    if (!session) {
      session = await this.userSessionRepository.findOne({
        where: { sessionId: event.sessionId },
      });
      if (session) {
        this.sessionCache.set(event.sessionId, session);
      }
    }

    if (session) {
      session.lastActivity = event.timestamp;
      session.eventCount++;
      if (event.eventType === 'page_view') {
        session.pageViews++;
      }
    }
  }

  private async checkUserConsent(userId: string): Promise<boolean> {
    const consent = await this.consentRecordRepository
      .createQueryBuilder('consent')
      .where('consent.userId = :userId', { userId })
      .andWhere('consent.consentType = :type', { type: 'analytics' })
      .orderBy('consent.timestamp', 'DESC')
      .getOne();

    return consent ? consent.granted : false;
  }

  private async calculateBehaviorMetrics(filters?: {
    timeRange?: string;
    userId?: string;
  }): Promise<UserBehaviorMetrics> {
    const since = filters?.timeRange ? this.parseTimeRange(filters.timeRange) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    const query = this.userEventRepository.createQueryBuilder('event')
      .where('event.timestamp >= :since', { since });
    
    if (filters?.userId) {
      query.andWhere('event.userId = :userId', { userId: filters.userId });
    }

    const totalEvents = await query.getCount();
    const uniqueUsers = await query.distinctOn(['event.userId']).getCount();
    
    // Calculate active users
    const dailyActiveUsers = await this.calculateActiveUsers(1, filters?.userId);
    const weeklyActiveUsers = await this.calculateActiveUsers(7, filters?.userId);
    const monthlyActiveUsers = await this.calculateActiveUsers(30, filters?.userId);

    // Calculate session metrics
    const sessionMetrics = await this.calculateSessionMetrics(since, filters?.userId);
    
    // Calculate retention
    const retention = await this.calculateRetentionRates(filters?.userId);
    
    // Calculate demographics
    const demographics = await this.calculateDemographics(since, filters?.userId);
    
    return {
      activeUsers: {
        daily: dailyActiveUsers,
        weekly: weeklyActiveUsers,
        monthly: monthlyActiveUsers,
      },
      sessions: sessionMetrics,
      engagement: {
        dailyActiveUsers,
        weeklyActiveUsers,
        monthlyActiveUsers,
        stickyFactor: dailyActiveUsers > 0 ? (dailyActiveUsers / monthlyActiveUsers) * 100 : 0,
      },
      retention,
      demographics,
      funnels: [], // Would be calculated based on specific funnel definitions
    };
  }

  private async calculateActiveUsers(days: number, userId?: string): Promise<number> {
    const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    
    const query = this.userEventRepository
      .createQueryBuilder('event')
      .where('event.timestamp >= :since', { since });
    
    if (userId) {
      query.andWhere('event.userId = :userId', { userId });
    }

    return await query.distinctOn(['event.userId']).getCount();
  }

  private async calculateSessionMetrics(since: Date, userId?: string): Promise<{
    total: number;
    averageDuration: number;
    bounceRate: number;
    averagePageViews: number;
  }> {
    const query = this.userSessionRepository
      .createQueryBuilder('session')
      .where('session.startTime >= :since', { since });
    
    if (userId) {
      query.andWhere('session.userId = :userId', { userId });
    }

    const sessions = await query.getMany();
    const totalSessions = sessions.length;
    
    if (totalSessions === 0) {
      return {
        total: 0,
        averageDuration: 0,
        bounceRate: 0,
        averagePageViews: 0,
      };
    }

    const totalDuration = sessions.reduce((sum, session) => sum + session.duration, 0);
    const totalPageViews = sessions.reduce((sum, session) => sum + session.pageViews, 0);
    const bouncedSessions = sessions.filter(session => session.pageViews <= 1 && session.duration < 30).length;

    return {
      total: totalSessions,
      averageDuration: totalDuration / totalSessions,
      bounceRate: (bouncedSessions / totalSessions) * 100,
      averagePageViews: totalPageViews / totalSessions,
    };
  }

  private async calculateRetentionRates(userId?: string): Promise<{
    day1: number;
    day7: number;
    day30: number;
  }> {
    // This would implement cohort retention analysis
    // For now, return placeholder values
    return {
      day1: 75,
      day7: 45,
      day30: 20,
    };
  }

  private async calculateDemographics(since: Date, userId?: string): Promise<{
    countries: Record<string, number>;
    platforms: Record<string, number>;
    devices: Record<string, number>;
  }> {
    const query = this.userEventRepository
      .createQueryBuilder('event')
      .where('event.timestamp >= :since', { since });
    
    if (userId) {
      query.andWhere('event.userId = :userId', { userId });
    }

    const events = await query.getMany();
    
    const countries: Record<string, number> = {};
    const platforms: Record<string, number> = {};
    const devices: Record<string, number> = {};

    events.forEach(event => {
      if (event.country) {
        countries[event.country] = (countries[event.country] || 0) + 1;
      }
      if (event.platform) {
        platforms[event.platform] = (platforms[event.platform] || 0) + 1;
      }
      // Extract device info from user agent
      const device = this.extractDeviceFromUserAgent(event.userAgent);
      if (device) {
        devices[device] = (devices[device] || 0) + 1;
      }
    });

    return { countries, platforms, devices };
  }

  private async calculateCommonPaths(since: Date, userId?: string): Promise<Array<{
    path: string;
    count: number;
    avgDuration: number;
  }>> {
    // This would implement path analysis
    // For now, return placeholder
    return [];
  }

  private async calculateDropOffPoints(since: Date, userId?: string): Promise<Array<{
    page: string;
    dropOffRate: number;
  }>> {
    // This would implement drop-off analysis
    // For now, return placeholder
    return [];
  }

  private async calculateEngagementPatterns(since: Date, userId?: string): Promise<Array<{
    hour: number;
    activeUsers: number;
    events: number;
  }>> {
    // This would implement engagement pattern analysis
    // For now, return placeholder
    return [];
  }

  private async calculateCohortAnalysis(cohortPeriod: string, userId?: string): Promise<Array<{
    cohort: string;
    users: number;
    retention: Record<string, number>;
  }>> {
    // This would implement cohort analysis
    // For now, return placeholder
    return [];
  }

  private async calculateOverallRetention(userId?: string): Promise<{
    day1: number;
    day7: number;
    day30: number;
  }> {
    // This would implement overall retention calculation
    // For now, return placeholder
    return {
      day1: 75,
      day7: 45,
      day30: 20,
    };
  }

  private anonymizeIp(ip?: string): string | undefined {
    if (!ip) return undefined;
    
    // For IPv4, remove last octet
    if (ip.includes('.')) {
      const parts = ip.split('.');
      return `${parts[0]}.${parts[1]}.${parts[2]}.0`;
    }
    
    // For IPv6, remove last 64 bits
    if (ip.includes(':')) {
      const parts = ip.split(':');
      return `${parts.slice(0, 4).join(':')}::`;
    }
    
    return ip;
  }

  private extractDeviceFromUserAgent(userAgent?: string): string | null {
    if (!userAgent) return null;
    
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'mobile';
    }
    if (/Tablet|iPad/.test(userAgent)) {
      return 'tablet';
    }
    return 'desktop';
  }

  private parseTimeRange(timeRange: string): Date {
    const now = new Date();
    const match = timeRange.match(/(\d+)([hdwm])/);
    
    if (!match) return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const value = parseInt(match[1]);
    const unit = match[2];
    
    switch (unit) {
      case 'h': return new Date(now.getTime() - value * 60 * 60 * 1000);
      case 'd': return new Date(now.getTime() - value * 24 * 60 * 60 * 1000);
      case 'w': return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000);
      case 'm': return new Date(now.getTime() - value * 30 * 24 * 60 * 60 * 1000);
      default: return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
  }
}